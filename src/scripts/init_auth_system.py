"""
Authentication System Initialization Script

Creates default roles and initial admin user for QAK multi-tenant system.
Run this script after setting up the database to initialize the authentication system.
"""

import asyncio
import os
import logging
from typing import Optional

from src.database.connection import initialize_database
from src.database.odm import initialize_odm, register_models
from src.database.models import User, Organization, Role, OrganizationMember
from src.database.models.role import RoleName, Permission
from src.database.models.organization import PlanType
from src.services.auth_service import AuthService

logger = logging.getLogger(__name__)


async def create_default_roles():
    """Create default system roles if they don't exist."""
    logger.info("Creating default roles...")
    
    default_roles = [
        {
            "name": RoleName.USER,
            "description": "Regular user with basic permissions",
            "permissions": [Permission.READ, Permission.WRITE],
            "is_system_role": True
        },
        {
            "name": RoleName.ORG_ADMIN,
            "description": "Organization administrator with management permissions",
            "permissions": [Permission.READ, Permission.WRITE, Permission.DELETE, Permission.ADMIN],
            "is_system_role": True
        },
        {
            "name": RoleName.ADMIN,
            "description": "System administrator with full access",
            "permissions": [Permission.READ, Permission.WRITE, Permission.DELETE, Permission.ADMIN],
            "is_system_role": True
        }
    ]
    
    created_roles = {}
    
    for role_data in default_roles:
        # Check if role already exists
        existing_role = await Role.find_one(Role.name == role_data["name"])
        
        if existing_role:
            logger.info(f"Role {role_data['name'].value} already exists")
            created_roles[role_data["name"]] = existing_role
        else:
            # Create new role
            role = Role(
                name=role_data["name"],
                description=role_data["description"],
                permissions=role_data["permissions"],
                is_system_role=role_data["is_system_role"]
            )
            await role.insert()
            created_roles[role_data["name"]] = role
            logger.info(f"Created role: {role_data['name'].value}")
    
    return created_roles


async def create_admin_user(
    email: str,
    password: str,
    full_name: str,
    org_name: str = "QAK Administration"
) -> tuple[User, Organization]:
    """Create initial admin user and organization."""
    logger.info(f"Creating admin user: {email}")
    
    auth_service = AuthService()
    
    # Check if admin user already exists
    existing_user = await User.find_one(User.email == email)
    if existing_user:
        logger.info(f"Admin user {email} already exists")
        # Get their organization
        memberships = await OrganizationMember.find(
            OrganizationMember.user_id == existing_user.user_id
        ).to_list()
        
        if memberships:
            org = await Organization.find_one(Organization.org_id == memberships[0].org_id)
            return existing_user, org
        else:
            raise Exception("Admin user exists but has no organization")
    
    # Create admin user with organization
    result = await auth_service.register_user(
        email=email,
        password=password,
        full_name=full_name,
        org_name=org_name
    )
    
    user = result["user"]
    organization = result["organization"]
    
    # Upgrade user to ADMIN role
    admin_role = await Role.find_one(Role.name == RoleName.ADMIN)
    if not admin_role:
        raise Exception("ADMIN role not found. Create default roles first.")
    
    # Find the user's membership and update to ADMIN role
    membership = await OrganizationMember.find_one(
        OrganizationMember.user_id == user.user_id,
        OrganizationMember.org_id == organization.org_id
    )
    
    if membership:
        membership.role_id = admin_role.role_id
        await membership.save()
        logger.info(f"Upgraded user {email} to ADMIN role")
    
    # Update organization to ENTERPRISE plan
    organization.plan_type = PlanType.ENTERPRISE
    await organization.save()
    logger.info(f"Upgraded organization {org_name} to ENTERPRISE plan")
    
    return user, organization


async def initialize_auth_system(
    admin_email: Optional[str] = None,
    admin_password: Optional[str] = None,
    admin_name: Optional[str] = None
):
    """Initialize the complete authentication system."""
    logger.info("🚀 Initializing QAK Authentication System...")
    
    try:
        # Initialize database connection
        logger.info("📡 Connecting to database...")
        await initialize_database()
        
        # Register models
        models = [User, Organization, Role, OrganizationMember]
        register_models(models)
        await initialize_odm()
        
        logger.info("✅ Database connection established")
        
        # Create default roles
        roles = await create_default_roles()
        logger.info(f"✅ Created/verified {len(roles)} default roles")
        
        # Create admin user if credentials provided
        if admin_email and admin_password and admin_name:
            user, org = await create_admin_user(
                email=admin_email,
                password=admin_password,
                full_name=admin_name
            )
            logger.info(f"✅ Created/verified admin user: {user.email}")
            logger.info(f"✅ Created/verified admin organization: {org.name}")
        else:
            logger.info("⚠️ No admin credentials provided, skipping admin user creation")
            logger.info("   Use environment variables or command line arguments to create admin user")
        
        logger.info("🎉 Authentication system initialization completed successfully!")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Authentication system initialization failed: {e}")
        raise


async def main():
    """Main function for running the initialization script."""
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    # Get admin credentials from environment variables
    admin_email = os.getenv("DEFAULT_ADMIN_EMAIL", "<EMAIL>")
    admin_password = os.getenv("DEFAULT_ADMIN_PASSWORD", "admin123456")
    admin_name = os.getenv("DEFAULT_ADMIN_NAME", "QAK Administrator")
    
    logger.info("QAK Authentication System Initialization")
    logger.info("=" * 50)
    
    if admin_email and admin_password and admin_name:
        logger.info(f"Admin user will be created: {admin_email}")
    else:
        logger.info("No admin credentials found in environment variables")
        logger.info("Set DEFAULT_ADMIN_EMAIL, DEFAULT_ADMIN_PASSWORD, and DEFAULT_ADMIN_NAME")
    
    try:
        await initialize_auth_system(
            admin_email=admin_email,
            admin_password=admin_password,
            admin_name=admin_name
        )
        
        logger.info("\n✅ Initialization completed successfully!")
        logger.info("You can now use the authentication system.")
        
        if admin_email:
            logger.info(f"\nAdmin login credentials:")
            logger.info(f"  Email: {admin_email}")
            logger.info(f"  Password: {admin_password}")
            logger.info(f"  Use these credentials to access admin endpoints.")
        
    except Exception as e:
        logger.error(f"\n❌ Initialization failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit_code = asyncio.run(main())
