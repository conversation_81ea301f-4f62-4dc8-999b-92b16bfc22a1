"""
OpenRouter Service Provider - Cost-effective LLM operations
Implements the BaseLLMService interface for OpenRouter integration
"""

import os
import logging
import base64
from typing import Dict, Any, List, Optional

from ..base_llm_service import BaseLLMService, LLMRequest, LLMResponse, MessageContent, ImageContent

logger = logging.getLogger(__name__)

# Try to import OpenRouter client - fallback to requests if not available
try:
    from openrouter_client import OpenRouterClient
    from openrouter_client.exceptions import AuthenticationError, RateLimitError, ValidationError
    OPENROUTER_AVAILABLE = True
    USE_REQUESTS_FALLBACK = False
except ImportError:
    try:
        # Alternative: try different import structure
        from openrouter import OpenRouter as OpenRouterClient
        OPENROUTER_AVAILABLE = True
        USE_REQUESTS_FALLBACK = False
    except ImportError:
        # Use requests implementation (our primary implementation)
        import requests
        OPENROUTER_AVAILABLE = True
        USE_REQUESTS_FALLBACK = True


class OpenRouterService(BaseLLMService):
    """OpenRouter LLM service provider with cost optimization and fallbacks."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize OpenRouter service.
        
        Args:
            api_key: OpenRouter API key (defaults to env OPENROUTER_API_KEY)
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        self._client = None
        self._available = False
        
        if not OPENROUTER_AVAILABLE:
            logger.warning("OpenRouter client library not available")
            return
        
        if self.api_key:
            try:
                if USE_REQUESTS_FALLBACK:
                    self._client = self._create_requests_client()
                    self._available = True
                else:
                    # Use official client
                    self._client = OpenRouterClient(
                        api_key=self.api_key,
                        max_retries=5,
                        rate_limit_buffer=0.2,
                        timeout=60.0
                    )
                    self._available = True
                    logger.info("OpenRouter service initialized with official client")
            except Exception as e:
                logger.error(f"Failed to initialize OpenRouter service: {e}")
                self._available = False
        
        # Model configurations optimized for each use case
        self.model_configs = {
            "gherkin": {
                "models": ["anthropic/claude-3-haiku", "openai/gpt-3.5-turbo"],
                "temperature": 0.1,
                "max_tokens": 2048
            },
            "validation": {
                "models": ["openai/gpt-3.5-turbo", "anthropic/claude-3-haiku"],
                "temperature": 0.0,
                "max_tokens": 1024
            },
            "translation": {
                "models": ["meta-llama/llama-3.1-8b-instruct", "openai/gpt-3.5-turbo"],
                "temperature": 0.1,
                "max_tokens": 1024
            },
            "enhancement": {
                "models": ["anthropic/claude-3-sonnet", "openai/gpt-4o-mini"],
                "temperature": 0.2,
                "max_tokens": 2048
            },
            "test_analysis": {
                "models": [
                    "google/gemini-2.5-flash",  # Best value for vision
                    "openai/gpt-4o-mini",      # Cheaper vision option
                    "anthropic/claude-3-haiku",  # Fast and cheap
                    "google/gemini-1.5-flash"    # Budget vision option
                ],
                "temperature": 0.1,
                "max_tokens": 2048  # Further reduced for maximum cost savings
            },
            "script_generation": {
                "models": [
                    "anthropic/claude-3-sonnet",  # Best for code generation
                    "openai/gpt-4o-mini",        # Good and cheap for code
                    "google/gemini-2.5-flash"     # Fast alternative
                ],
                "temperature": 0.1,  # Low temperature for consistent code
                "max_tokens": 4096   # Allow longer scripts
            },
            "general": {
                "models": ["openai/gpt-4o-mini", "anthropic/claude-3-haiku"],
                "temperature": 0.1,
                "max_tokens": 2048
            }
        }
    
    def _create_requests_client(self):
        """Create a requests-based client for OpenRouter API calls."""
        return {
            "api_key": self.api_key,
            "base_url": "https://openrouter.ai/api/v1",
            "headers": {
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/nahuelcioffi/qak",
                "X-Title": "QAK Agent",
            }
        }
    
    def _make_requests_based_call(self, request_params: Dict[str, Any]) -> Dict[str, Any]:
        """Make OpenRouter API call using requests library."""
        import requests
        
        url = f"{self._client['base_url']}/chat/completions"
        
        response = requests.post(
            url,
            headers=self._client["headers"],
            json=request_params,
            timeout=60
        )
        
        if response.status_code == 401:
            raise Exception("Authentication error - check OPENROUTER_API_KEY")
        elif response.status_code == 429:
            raise Exception("Rate limit exceeded")
        elif response.status_code != 200:
            raise Exception(f"HTTP {response.status_code}: {response.text}")
        
        return response.json()
    
    def _format_messages_for_openrouter(self, messages: List[Dict]) -> List[Dict]:
        """Format messages for OpenRouter API, converting images to base64 format."""
        formatted_messages = []
        
        for message in messages:
            role = message["role"]
            content = message["content"]
            
            if isinstance(content, list):
                # Mixed content (text + images)
                formatted_content = []
                
                for content_item in content:
                    if isinstance(content_item, MessageContent):
                        if content_item.text:
                            formatted_content.append({
                                "type": "text",
                                "text": content_item.text
                            })
                        if content_item.image:
                            formatted_content.append({
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{content_item.image.mime_type};base64,{content_item.image.data}"
                                }
                            })
                    elif isinstance(content_item, dict):
                        # Handle dict format
                        if "text" in content_item:
                            formatted_content.append({
                                "type": "text", 
                                "text": content_item["text"]
                            })
                        if "image" in content_item:
                            image_data = content_item["image"]
                            mime_type = image_data.get("mime_type", "image/png")
                            formatted_content.append({
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:{mime_type};base64,{image_data['data']}"
                                }
                            })
                
                formatted_messages.append({
                    "role": role,
                    "content": formatted_content
                })
            else:
                # Text only
                formatted_messages.append({
                    "role": role,
                    "content": content
                })
        
        return formatted_messages
    
    def is_available(self) -> bool:
        """Check if OpenRouter service is available."""
        return OPENROUTER_AVAILABLE and self._available and self._client is not None
    
    def get_provider_name(self) -> str:
        """Get provider name."""
        return "openrouter"
    
    def get_supported_use_cases(self) -> List[str]:
        """Get supported use cases."""
        return list(self.model_configs.keys())
    
    def make_request(self, request: LLMRequest) -> LLMResponse:
        """Make request to OpenRouter with automatic model fallback and vision support."""
        if not self.is_available():
            return LLMResponse(
                content="",
                model_used="openrouter",
                success=False,
                error="OpenRouter service not available"
            )
        
        # Get configuration for use case
        config = self.model_configs.get(request.use_case, self.model_configs["general"])
        models_to_try = config["models"].copy()
        
        # If request has images, prioritize vision-capable models
        if request.has_images:
            vision_models = [
                "google/gemini-2.5-flash",  # Gemini 2.5 Flash prioritized first
                "openai/gpt-4o-mini", 
                "openai/gpt-4o",
                "anthropic/claude-3-sonnet",
                "anthropic/claude-3-haiku",
                "google/gemini-2.5-flash"
            ]
            # Put vision models first
            models_to_try = [m for m in vision_models if m not in models_to_try] + models_to_try
        else:
            # Add premium fallbacks for text-only
            premium_fallbacks = [
                "anthropic/claude-3-sonnet",
                "openai/gpt-4o-mini",
                "anthropic/claude-3-haiku"
            ]
            models_to_try.extend([m for m in premium_fallbacks if m not in models_to_try])
        
        # Try each model in sequence
        last_error = None
        
        for model in models_to_try:
            try:
                logger.info(f"Trying OpenRouter model {model} for {request.use_case}")
                
                # Prepare request parameters with vision support
                formatted_messages = self._format_messages_for_openrouter(request.messages)
                request_params = {
                    "model": model,
                    "messages": formatted_messages,
                    "temperature": request.temperature or config.get("temperature", 0.1),
                    "max_tokens": request.max_tokens or config.get("max_tokens", 2048)
                }
                
                # Make request - handle both client types
                if USE_REQUESTS_FALLBACK:
                    # Use requests-based implementation
                    response_data = self._make_requests_based_call(request_params)
                    
                    # Extract content and usage from response
                    content = response_data["choices"][0]["message"]["content"]
                    usage = None
                    if "usage" in response_data:
                        usage = {
                            "prompt_tokens": response_data["usage"]["prompt_tokens"],
                            "completion_tokens": response_data["usage"]["completion_tokens"],
                            "total_tokens": response_data["usage"]["total_tokens"]
                        }
                else:
                    # Use official client
                    response = self._client.chat.create(**request_params)
                    content = response.choices[0].message.content
                    
                    # Extract usage information
                    usage = None
                    if response.usage:
                        usage = {
                            "prompt_tokens": response.usage.prompt_tokens,
                            "completion_tokens": response.usage.completion_tokens,
                            "total_tokens": response.usage.total_tokens
                        }
                
                logger.info(f"Success with OpenRouter model {model} for {request.use_case}")
                
                return LLMResponse(
                    content=content,
                    model_used=model,
                    usage=usage,
                    metadata={
                        "provider": "openrouter",
                        "use_case": request.use_case,
                        "model_attempted": model,
                        "implementation": "requests" if USE_REQUESTS_FALLBACK else "official"
                    },
                    success=True
                )
                
            except Exception as e:
                error_str = str(e)
                
                # Handle different error types based on implementation
                if USE_REQUESTS_FALLBACK:
                    # For requests-based implementation
                    if "Authentication error" in error_str:
                        # Non-recoverable authentication error
                        logger.error(f"Non-recoverable OpenRouter authentication error with model {model}: {e}")
                        return LLMResponse(
                            content="",
                            model_used=model,
                            success=False,
                            error=f"Authentication error: {e}"
                        )
                    elif "Rate limit exceeded" in error_str:
                        # Rate limit - try next model
                        logger.warning(f"Rate limited on OpenRouter model {model}, trying next: {e}")
                        last_error = error_str
                        continue
                    else:
                        # Other errors - try next model
                        logger.warning(f"Error with OpenRouter model {model}, trying next: {e}")
                        last_error = error_str
                        continue
                else:
                    # For official client implementation
                    if hasattr(e, '__class__') and e.__class__.__name__ in ['AuthenticationError', 'ValidationError']:
                        # Non-recoverable errors
                        logger.error(f"Non-recoverable OpenRouter error with model {model}: {e}")
                        return LLMResponse(
                            content="",
                            model_used=model,
                            success=False,
                            error=f"Authentication/Validation error: {e}"
                        )
                    elif hasattr(e, '__class__') and e.__class__.__name__ == 'RateLimitError':
                        # Rate limit - try next model
                        logger.warning(f"Rate limited on OpenRouter model {model}, trying next: {e}")
                        last_error = error_str
                        continue
                    else:
                        # Other errors - try next model
                        logger.warning(f"Error with OpenRouter model {model}, trying next: {e}")
                        last_error = error_str
                        continue
        
        # All models failed
        error_msg = f"All OpenRouter models failed for {request.use_case}. Last error: {last_error}"
        logger.error(error_msg)
        
        return LLMResponse(
            content="",
            model_used="openrouter",
            success=False,
            error=error_msg
        )
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """Get OpenRouter usage statistics."""
        if not self.is_available():
            return {"status": "unavailable", "error": "Service not available"}
        
        try:
            # Get credits
            credits = self._client.credits.get()
            
            # Get rate limits  
            rate_limits = self._client.calculate_rate_limits()
            
            return {
                "provider": "openrouter",
                "status": "available",
                "credits": {
                    "balance": credits.data.credits,
                    "usage": credits.data.usage
                },
                "rate_limits": rate_limits,
                "model_configs": self.model_configs
            }
            
        except Exception as e:
            logger.error(f"Error getting OpenRouter usage stats: {e}")
            return {
                "provider": "openrouter", 
                "status": "error",
                "error": str(e)
            }
    
    def _make_requests_based_call(self, request_params: Dict[str, Any]) -> Dict[str, Any]:
        """Make OpenRouter API call using requests library."""
        import requests
        import json
        
        url = f"{self._client['base_url']}/chat/completions"
        
        response = requests.post(
            url,
            headers=self._client["headers"],
            json=request_params,
            timeout=60
        )
        
        if response.status_code == 401:
            raise Exception("Authentication error - check OPENROUTER_API_KEY")
        elif response.status_code == 429:
            raise Exception("Rate limit exceeded")
        elif response.status_code != 200:
            raise Exception(f"HTTP {response.status_code}: {response.text}")
        
        return response.json()
