"""
Authentication Service for QAK Multi-Tenant System

Handles password hashing, JWT tokens, user registration, login, and logout.
"""

import os
import uuid
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any
from passlib.context import CryptContext
from jose import JWTError, jwt

from src.database.models.user import User
from src.database.models.organization import Organization, PlanType
from src.database.models.role import Role, RoleName
from src.database.models.organization_member import OrganizationMember
from src.database.repositories.user_repository import UserRepository
from src.database.repositories.organization_repository import OrganizationRepository
from src.database.repositories.organization_member_repository import OrganizationMemberRepository
from src.database.exceptions import DatabaseError, DocumentNotFoundError

logger = logging.getLogger(__name__)

# Password hashing configuration
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# JWT configuration from environment variables
SECRET_KEY = os.getenv("JWT_SECRET_KEY", "your-secret-key-change-this-in-production")
ALGORITHM = os.getenv("JWT_ALGORITHM", "HS256")
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("JWT_ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
REFRESH_TOKEN_EXPIRE_DAYS = int(os.getenv("JWT_REFRESH_TOKEN_EXPIRE_DAYS", "7"))


class AuthService:
    """Authentication service for user management and JWT tokens."""
    
    def __init__(self):
        self.user_repo = UserRepository()
        self.org_repo = OrganizationRepository()
        self.member_repo = OrganizationMemberRepository()
        # In a production system, you'd want to use Redis or a database for token blacklist
        self.blacklisted_tokens = set()
    
    # Password Management
    
    def hash_password(self, password: str) -> str:
        """Hash a password using bcrypt."""
        return pwd_context.hash(password)
    
    def verify_password(self, plain_password: str, hashed_password: str) -> bool:
        """Verify a password against its hash."""
        return pwd_context.verify(plain_password, hashed_password)
    
    # JWT Token Management
    
    def create_access_token(self, user_id: str, org_id: str, expires_delta: Optional[timedelta] = None) -> str:
        """Create a JWT access token."""
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
        
        to_encode = {
            "sub": user_id,
            "org_id": org_id,
            "exp": expire,
            "type": "access"
        }
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    def create_refresh_token(self, user_id: str) -> str:
        """Create a JWT refresh token."""
        expire = datetime.utcnow() + timedelta(days=REFRESH_TOKEN_EXPIRE_DAYS)
        to_encode = {
            "sub": user_id,
            "exp": expire,
            "type": "refresh"
        }
        encoded_jwt = jwt.encode(to_encode, SECRET_KEY, algorithm=ALGORITHM)
        return encoded_jwt
    
    def verify_token(self, token: str) -> Dict[str, Any]:
        """Verify and decode a JWT token."""
        try:
            if token in self.blacklisted_tokens:
                raise JWTError("Token has been blacklisted")
            
            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            user_id: str = payload.get("sub")
            if user_id is None:
                raise JWTError("Invalid token payload")
            
            return payload
        except JWTError as e:
            logger.warning(f"Token verification failed: {e}")
            raise JWTError(f"Token verification failed: {e}")
    
    def refresh_token(self, refresh_token: str) -> str:
        """Create a new access token from a refresh token."""
        try:
            payload = self.verify_token(refresh_token)
            if payload.get("type") != "refresh":
                raise JWTError("Invalid token type")
            
            user_id = payload.get("sub")
            # For refresh, we need to get the user's current organization
            # This is a simplified approach - in production you might want to handle this differently
            org_id = payload.get("org_id", "")  # Default to empty if not present
            
            return self.create_access_token(user_id, org_id)
        except JWTError as e:
            logger.warning(f"Token refresh failed: {e}")
            raise JWTError(f"Token refresh failed: {e}")
    
    # User Authentication
    
    async def register_user(
        self, 
        email: str, 
        password: str, 
        full_name: str, 
        org_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Register a new user and optionally create an organization."""
        try:
            # Check if email already exists
            if await self.user_repo.email_exists(email):
                raise ValueError("Email already registered")
            
            # Create user
            hashed_password = self.hash_password(password)
            user_id = f"user_{uuid.uuid4().hex[:12]}"
            user = User(
                user_id=user_id,
                email=email,
                password_hash=hashed_password,
                full_name=full_name
            )
            user = await self.user_repo.create(user)
            
            # Create organization if provided
            organization = None
            if org_name:
                org_id = f"org_{uuid.uuid4().hex[:12]}"
                # Generate slug from organization name
                slug = org_name.lower().replace(" ", "-").replace("_", "-")
                slug = "".join(c for c in slug if c.isalnum() or c == "-")
                slug = f"{slug}-{uuid.uuid4().hex[:6]}"  # Add unique suffix

                organization = Organization(
                    org_id=org_id,
                    name=org_name,
                    slug=slug,
                    plan_type=PlanType.FREE,
                    created_by=user_id
                )
                organization = await self.org_repo.create(organization)
                
                # Get default USER role
                user_role = await Role.find_one(Role.name == RoleName.USER)
                if not user_role:
                    # Create default role if it doesn't exist
                    user_role = Role(
                        name=RoleName.USER,
                        description="Regular user with basic permissions",
                        permissions=["read", "write"]
                    )
                    await user_role.insert()
                
                # Add user to organization
                member_id = f"member_{uuid.uuid4().hex[:12]}"
                membership = OrganizationMember(
                    member_id=member_id,
                    user_id=user.user_id,
                    org_id=organization.org_id,
                    role_id=user_role.role_id
                )
                await self.member_repo.create(membership)
            
            logger.info(f"User registered successfully: {user.email}")
            
            return {
                "user": user,
                "organization": organization,
                "message": "User registered successfully"
            }
            
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"User registration failed: {e}")
            raise DatabaseError(f"User registration failed: {e}")
    
    async def login_user(self, email: str, password: str) -> Dict[str, Any]:
        """Authenticate user and return tokens."""
        try:
            # Get user by email
            user = await self.user_repo.get_by_email(email)
            if not user:
                raise ValueError("Invalid email or password")
            
            # Check if user is active
            if not user.is_active:
                raise ValueError("User account is deactivated")
            
            # Verify password
            if not self.verify_password(password, user.password_hash):
                raise ValueError("Invalid email or password")
            
            # Get user's organizations
            memberships = await self.member_repo.get_user_organizations(user.user_id)
            if not memberships:
                raise ValueError("User has no organization memberships")
            
            # Use the first active organization for the token
            # In a real app, you might want to let the user choose
            primary_membership = memberships[0]
            
            # Create tokens
            access_token = self.create_access_token(user.user_id, primary_membership.org_id)
            refresh_token = self.create_refresh_token(user.user_id)
            
            # Update last login
            await self.user_repo.update_last_login(user.user_id)
            
            logger.info(f"User logged in successfully: {user.email}")
            
            return {
                "access_token": access_token,
                "refresh_token": refresh_token,
                "token_type": "bearer",
                "user": user,
                "organization_id": primary_membership.org_id,
                "expires_in": ACCESS_TOKEN_EXPIRE_MINUTES * 60
            }
            
        except ValueError:
            raise
        except Exception as e:
            logger.error(f"User login failed: {e}")
            raise DatabaseError(f"User login failed: {e}")
    
    def logout_user(self, token: str) -> bool:
        """Logout user by blacklisting the token."""
        try:
            # Verify token first
            payload = self.verify_token(token)
            
            # Add to blacklist
            self.blacklisted_tokens.add(token)
            
            user_id = payload.get("sub")
            logger.info(f"User logged out successfully: {user_id}")
            return True
            
        except JWTError:
            # Token is already invalid, consider logout successful
            return True
        except Exception as e:
            logger.error(f"Logout failed: {e}")
            return False
