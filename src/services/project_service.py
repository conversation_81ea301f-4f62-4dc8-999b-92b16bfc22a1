"""
Project Service for QAK MongoDB Integration

Database-backed project service that integrates ProjectRepository with business logic.
Maintains compatibility with existing ProjectManagerService while adding new capabilities.
"""

import logging
import json
import os
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path

from .base_service import BaseService, DatabaseServiceMixin, ServiceResult, ServiceException
from .base_service import handle_service_exceptions, log_service_operation
from ..database.models.project import Project, TestSuite, TestCase, TestStatus, GitHubConfig
from ..database.repositories import ProjectRepository
from ..utilities.project_manager import Project as LegacyProject  # Legacy import

logger = logging.getLogger(__name__)


class ProjectService(BaseService, DatabaseServiceMixin):
    """
    Database-backed project service with legacy compatibility.
    
    Features:
    - Full CRUD operations using MongoDB
    - Legacy JSON file compatibility
    - Advanced search and analytics
    - Batch operations and bulk updates
    - Data migration utilities
    - Performance monitoring
    """
    
    def __init__(self, legacy_base_dir: str = "projects"):
        """
        Initialize project service.
        
        Args:
            legacy_base_dir: Base directory for legacy JSON files (for migration)
        """
        super().__init__("projects")
        if not hasattr(self, "_repositories"):
            self._repositories = {}
        self.legacy_base_dir = Path(legacy_base_dir)
        self._project_repo = None
    
    @property
    def project_repo(self) -> ProjectRepository:
        """Get project repository instance."""
        if self._project_repo is None:
            self._project_repo = self.get_repository("project")
        return self._project_repo
    
    # Health Check & Service Info
    
    async def health_check(self) -> ServiceResult[Dict[str, Any]]:
        """Perform project service health check."""
        try:
            health_info = {
                "service": self.service_name,
                "status": "healthy",
                "database_enabled": self.should_use_database(),
                "legacy_dir_exists": self.legacy_base_dir.exists(),
                "repositories": ["project"]
            }
            
            if self.should_use_database():
                db_health = await self.check_database_health()
                health_info["database"] = db_health
            
            return ServiceResult.success_result(health_info)
            
        except Exception as e:
            return self.handle_service_error(e, "health_check")
    
    async def get_service_info(self) -> ServiceResult[Dict[str, Any]]:
        """Get project service information and statistics."""
        try:
            info = {
                "service": self.service_name,
                "version": "1.0.0",
                "features": {
                    "database_backend": self.should_use_database(),
                    "legacy_support": True,
                    "batch_operations": True,
                    "advanced_search": True,
                    "analytics": True
                },
                "performance": self.get_performance_metrics()
            }
            
            if self.should_use_database():
                # Get database statistics
                try:
                    project_count = await self.project_repo.count({})
                    info["statistics"] = {
                        "total_projects": project_count,
                        "repository": "mongodb"
                    }
                except Exception as e:
                    self.logger.warning(f"Could not get database statistics: {e}")
                    info["statistics"] = {"error": "Database not available"}
            else:
                # Get legacy file statistics
                info["statistics"] = self._get_legacy_file_stats()
            
            return ServiceResult.success_result(info)
            
        except Exception as e:
            return self.handle_service_error(e, "get_service_info")
    
    # Project CRUD Operations
    
    @handle_service_exceptions
    @log_service_operation
    async def create_project(
        self,
        name: str,
        description: str = "",
        tags: Optional[List[str]] = None,
        github_config: Optional[Dict[str, Any]] = None
    ) -> ServiceResult[Project]:
        """
        Create a new project.
        
        Args:
            name: Project name
            description: Project description
            tags: Project tags
            github_config: GitHub configuration dict
            
        Returns:
            ServiceResult containing the created project
        """
        # Validate input
        self.validate_required_fields({"name": name}, ["name"])
        
        if tags is None:
            tags = []
        
        # Generate project ID
        project_id = self._generate_project_id(name)
        
        # Create GitHub config if provided
        gh_config = None
        if github_config:
            gh_config = GitHubConfig(**github_config)
        
        # Create project model
        project = Project(
            project_id=project_id,
            name=name,
            description=description,
            tags=tags,
            github_config=gh_config
        )
        
        # Save to database
        if self.should_use_database():
            created_project = await self.project_repo.create_project(project)
            self.log_operation("create_project", {
                "project_id": project_id,
                "backend": "database"
            })
            return ServiceResult.success_result(created_project)
        else:
            # Legacy mode - save as JSON file
            legacy_project = self._convert_to_legacy_project(project)
            success = self._save_legacy_project(legacy_project)
            if success:
                self.log_operation("create_project", {
                    "project_id": project_id,
                    "backend": "legacy"
                })
                return ServiceResult.success_result(project)
            else:
                raise ServiceException("Failed to save project to legacy storage")
    
    @handle_service_exceptions
    @log_service_operation
    async def get_project(self, project_id: str) -> ServiceResult[Optional[Project]]:
        """
        Get a project by ID.
        
        Args:
            project_id: Project identifier
            
        Returns:
            ServiceResult containing the project or None if not found
        """
        self.validate_id_format(project_id, "project")
        
        if self.should_use_database():
            project = await self.project_repo.get_by_project_id(project_id)
            return ServiceResult.success_result(project)
        else:
            # Legacy mode - load from JSON file
            legacy_project = self._load_legacy_project(project_id)
            if legacy_project:
                project = self._convert_from_legacy_project(legacy_project)
                return ServiceResult.success_result(project)
            else:
                return ServiceResult.success_result(None)
    
    @handle_service_exceptions
    @log_service_operation
    async def update_project(
        self,
        project_id: str,
        name: Optional[str] = None,
        description: Optional[str] = None,
        tags: Optional[List[str]] = None,
        github_config: Optional[Dict[str, Any]] = None
    ) -> ServiceResult[Optional[Project]]:
        """
        Update an existing project.
        
        Args:
            project_id: Project identifier
            name: New project name
            description: New project description
            tags: New project tags
            github_config: New GitHub configuration
            
        Returns:
            ServiceResult containing the updated project
        """
        self.validate_id_format(project_id, "project")
        
        # Get existing project
        get_result = await self.get_project(project_id)
        if not get_result.success:
            return get_result
        
        project = get_result.data
        if not project:
            return ServiceResult.error_result("Project not found", "NOT_FOUND")
        
        # Update fields if provided
        if name is not None:
            self.validate_required_fields({"name": name}, ["name"])
            project.name = name
        if description is not None:
            project.description = description
        if tags is not None:
            project.tags = tags
        if github_config is not None:
            project.github_config = GitHubConfig(**github_config) if github_config else None
        
        # Save updated project
        if self.should_use_database():
            updated_project = await self.project_repo.update_project(project)
            return ServiceResult.success_result(updated_project)
        else:
            # Legacy mode
            legacy_project = self._convert_to_legacy_project(project)
            success = self._save_legacy_project(legacy_project)
            if success:
                return ServiceResult.success_result(project)
            else:
                raise ServiceException("Failed to update project in legacy storage")
    
    @handle_service_exceptions
    @log_service_operation
    async def delete_project(self, project_id: str) -> ServiceResult[bool]:
        """
        Delete a project.
        
        Args:
            project_id: Project identifier
            
        Returns:
            ServiceResult containing success status
        """
        self.validate_id_format(project_id, "project")
        
        if self.should_use_database():
            success = await self.project_repo.delete_by_project_id(project_id)
            return ServiceResult.success_result(success)
        else:
            # Legacy mode
            success = self._delete_legacy_project(project_id)
            return ServiceResult.success_result(success)
    
    @handle_service_exceptions
    @log_service_operation
    async def list_projects(
        self,
        tags: Optional[List[str]] = None,
        search_term: Optional[str] = None,
        page: int = 1,
        page_size: int = 20
    ) -> ServiceResult[Dict[str, Any]]:
        """
        List projects with optional filtering and pagination.
        
        Args:
            tags: Filter by tags
            search_term: Search in name and description
            page: Page number (1-based)
            page_size: Number of items per page
            
        Returns:
            ServiceResult containing paginated project list
        """
        if self.should_use_database():
            if search_term:
                # Use search functionality
                result = await self.project_repo.search_projects(
                    search_term=search_term,
                    tags=tags,
                    page=page,
                    page_size=page_size
                )
                return ServiceResult.success_result({
                    "projects": result.items,
                    "pagination": {
                        "page": result.page,
                        "page_size": result.page_size,
                        "total_items": result.total,
                        "total_pages": result.total_pages
                    }
                })
            else:
                # Use regular find with filters
                projects = await self.project_repo.find_by_tags(tags) if tags else await self.project_repo.get_all()
                
                # Manual pagination for non-search queries
                start = (page - 1) * page_size
                end = start + page_size
                paginated_projects = projects[start:end]
                
                return ServiceResult.success_result({
                    "projects": paginated_projects,
                    "pagination": {
                        "page": page,
                        "page_size": page_size,
                        "total_items": len(projects),
                        "total_pages": (len(projects) + page_size - 1) // page_size
                    }
                })
        else:
            # Legacy mode
            legacy_projects = self._load_all_legacy_projects()
            projects = [self._convert_from_legacy_project(lp) for lp in legacy_projects]
            
            # Apply filters
            if tags:
                projects = [p for p in projects if any(tag in p.tags for tag in tags)]
            if search_term:
                search_lower = search_term.lower()
                projects = [
                    p for p in projects 
                    if search_lower in p.name.lower() or search_lower in p.description.lower()
                ]
            
            # Manual pagination
            start = (page - 1) * page_size
            end = start + page_size
            paginated_projects = projects[start:end]
            
            return ServiceResult.success_result({
                "projects": paginated_projects,
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total_items": len(projects),
                    "total_pages": (len(projects) + page_size - 1) // page_size
                }
            })
    
    # Test Suite Operations
    
    @handle_service_exceptions
    @log_service_operation
    async def create_test_suite(
        self,
        project_id: str,
        name: str,
        description: str = "",
        tags: Optional[List[str]] = None
    ) -> ServiceResult[Optional[TestSuite]]:
        """Create a test suite within a project."""
        self.validate_id_format(project_id, "project")
        self.validate_required_fields({"name": name}, ["name"])
        
        # Get the project
        get_result = await self.get_project(project_id)
        if not get_result.success:
            return get_result
        
        project = get_result.data
        if not project:
            return ServiceResult.error_result("Project not found", "NOT_FOUND")
        
        # Create test suite
        suite_id = self._generate_suite_id(name)
        test_suite = TestSuite(
            suite_id=suite_id,
            name=name,
            description=description,
            tags=tags or []
        )
        
        # Add to project
        project.add_test_suite(test_suite)
        
        # Save project
        if self.should_use_database():
            await self.project_repo.update_project(project)
        else:
            legacy_project = self._convert_to_legacy_project(project)
            self._save_legacy_project(legacy_project)
        
        return ServiceResult.success_result(test_suite)
    
    @handle_service_exceptions
    @log_service_operation
    async def create_test_case(
        self,
        project_id: str,
        suite_id: str,
        name: str,
        description: str = "",
        instrucciones: str = "",
        historia_de_usuario: str = "",
        gherkin: str = "",
        url: str = "",
        tags: Optional[List[str]] = None
    ) -> ServiceResult[Optional[TestCase]]:
        """Create a test case within a test suite."""
        self.validate_id_format(project_id, "project")
        self.validate_id_format(suite_id, "suite")
        self.validate_required_fields({"name": name}, ["name"])
        
        # Get the project
        get_result = await self.get_project(project_id)
        if not get_result.success:
            return get_result
        
        project = get_result.data
        if not project:
            return ServiceResult.error_result("Project not found", "NOT_FOUND")
        
        # Get the test suite
        test_suite = project.get_test_suite(suite_id)
        if not test_suite:
            return ServiceResult.error_result("Test suite not found", "NOT_FOUND")
        
        # Create test case
        test_id = self._generate_test_id(name)
        test_case = TestCase(
            test_id=test_id,
            name=name,
            description=description,
            instrucciones=instrucciones,
            historia_de_usuario=historia_de_usuario,
            gherkin=gherkin,
            url=url,
            tags=tags or []
        )
        
        # Add to suite
        test_suite.add_test_case(test_case)
        
        # Save project
        if self.should_use_database():
            await self.project_repo.update_project(project)
        else:
            legacy_project = self._convert_to_legacy_project(project)
            self._save_legacy_project(legacy_project)
        
        return ServiceResult.success_result(test_case)
    
    # Analytics and Reporting
    
    @handle_service_exceptions
    @log_service_operation
    async def get_project_analytics(self) -> ServiceResult[Dict[str, Any]]:
        """Get project analytics and statistics."""
        if self.should_use_database():
            analytics = await self.project_repo.get_project_analytics()
            return ServiceResult.success_result(analytics)
        else:
            # Legacy analytics
            legacy_projects = self._load_all_legacy_projects()
            analytics = self._calculate_legacy_analytics(legacy_projects)
            return ServiceResult.success_result(analytics)
    
    # Migration Utilities
    
    @handle_service_exceptions
    @log_service_operation
    async def migrate_from_legacy(
        self,
        dry_run: bool = True,
        batch_size: int = 10
    ) -> ServiceResult[Dict[str, Any]]:
        """
        Migrate projects from legacy JSON files to database.
        
        Args:
            dry_run: If True, only simulate migration without saving
            batch_size: Number of projects to process in each batch
            
        Returns:
            ServiceResult containing migration statistics
        """
        if not self.should_use_database():
            return ServiceResult.error_result(
                "Database backend not enabled",
                "DATABASE_DISABLED"
            )
        
        migration_stats = {
            "total_projects": 0,
            "migrated_projects": 0,
            "failed_projects": 0,
            "errors": [],
            "dry_run": dry_run
        }
        
        try:
            # Load all legacy projects
            legacy_projects = self._load_all_legacy_projects()
            migration_stats["total_projects"] = len(legacy_projects)
            
            # Process in batches
            for i in range(0, len(legacy_projects), batch_size):
                batch = legacy_projects[i:i + batch_size]
                
                for legacy_project in batch:
                    try:
                        # Convert to new model
                        project = self._convert_from_legacy_project(legacy_project)
                        
                        if not dry_run:
                            # Check if already exists
                            existing = await self.project_repo.get_by_project_id(project.project_id)
                            if existing:
                                self.logger.warning(f"Project {project.project_id} already exists, skipping")
                                continue
                            
                            # Save to database
                            await self.project_repo.create_project(project)
                        
                        migration_stats["migrated_projects"] += 1
                        
                    except Exception as e:
                        migration_stats["failed_projects"] += 1
                        migration_stats["errors"].append({
                            "project_id": legacy_project.project_id,
                            "error": str(e)
                        })
                        self.logger.error(f"Failed to migrate project {legacy_project.project_id}: {e}")
            
            return ServiceResult.success_result(migration_stats)
            
        except Exception as e:
            migration_stats["errors"].append({"general_error": str(e)})
            return ServiceResult.success_result(migration_stats)  # Return partial results
    
    # Helper Methods
    
    def _generate_project_id(self, name: str) -> str:
        """Generate a unique project ID from name."""
        import uuid
        base_id = name.lower().replace(' ', '_').replace('-', '_')
        base_id = ''.join(c for c in base_id if c.isalnum() or c == '_')
        return f"{base_id}_{uuid.uuid4().hex[:8]}"
    
    def _generate_suite_id(self, name: str) -> str:
        """Generate a unique suite ID from name."""
        import uuid
        base_id = name.lower().replace(' ', '_').replace('-', '_')
        base_id = ''.join(c for c in base_id if c.isalnum() or c == '_')
        return f"suite_{base_id}_{uuid.uuid4().hex[:6]}"
    
    def _generate_test_id(self, name: str) -> str:
        """Generate a unique test ID from name."""
        import uuid
        base_id = name.lower().replace(' ', '_').replace('-', '_')
        base_id = ''.join(c for c in base_id if c.isalnum() or c == '_')
        return f"test_{base_id}_{uuid.uuid4().hex[:6]}"
    
    # Legacy Compatibility Methods
    
    def _convert_to_legacy_project(self, project: Project) -> LegacyProject:
        """Convert database model to legacy project."""
        # This would implement the conversion logic
        # For now, return a placeholder
        return LegacyProject(project.project_id, project.name, project.description)
    
    def _convert_from_legacy_project(self, legacy_project: LegacyProject) -> Project:
        """Convert legacy project to database model."""
        return Project.from_legacy_json(legacy_project.to_dict())
    
    def _save_legacy_project(self, legacy_project: LegacyProject) -> bool:
        """Save project to legacy JSON file."""
        try:
            project_dir = self.legacy_base_dir / "projects" / legacy_project.project_id
            project_dir.mkdir(parents=True, exist_ok=True)
            
            project_file = project_dir / f"{legacy_project.project_id}.json"
            with open(project_file, 'w', encoding='utf-8') as f:
                json.dump(legacy_project.to_dict(), f, indent=2, ensure_ascii=False)
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to save legacy project: {e}")
            return False
    
    def _load_legacy_project(self, project_id: str) -> Optional[LegacyProject]:
        """Load project from legacy JSON file."""
        try:
            project_file = self.legacy_base_dir / "projects" / project_id / f"{project_id}.json"
            if project_file.exists():
                with open(project_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                return LegacyProject.from_dict(data)
            return None
        except Exception as e:
            self.logger.error(f"Failed to load legacy project {project_id}: {e}")
            return None
    
    def _load_all_legacy_projects(self) -> List[LegacyProject]:
        """Load all legacy projects from JSON files."""
        projects = []
        for file_path in self.legacy_base_dir.glob("**/*.json"):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                project = LegacyProject.from_dict(data)
                projects.append(project)
            except Exception as e:
                self.logger.error(f"Failed to load legacy project {file_path}: {e}")
        
        return projects
    
    def _delete_legacy_project(self, project_id: str) -> bool:
        """Delete legacy project directory and files."""
        try:
            project_dir = self.legacy_base_dir / "projects" / project_id
            if project_dir.exists():
                import shutil
                shutil.rmtree(project_dir)
                return True
            return False
        except Exception as e:
            self.logger.error(f"Failed to delete legacy project {project_id}: {e}")
            return False
    
    def _get_legacy_file_stats(self) -> Dict[str, Any]:
        """Get statistics from legacy JSON files."""
        projects = self._load_all_legacy_projects()
        return {
            "total_projects": len(projects),
            "repository": "legacy_json"
        }
    
    def _calculate_legacy_analytics(self, projects: List[LegacyProject]) -> Dict[str, Any]:
        """Calculate analytics from legacy projects."""
        return {
            "total_projects": len(projects),
            "total_test_suites": sum(len(p.test_suites) for p in projects),
            "total_test_cases": sum(
                len(suite.test_cases) for p in projects for suite in p.test_suites.values()
            ),
            "source": "legacy_files"
        }
    
    @handle_service_exceptions
    @log_service_operation
    async def export_project(self, project_id: str, file_path: str | None = None) -> ServiceResult[dict]:
        """
        Export a project to a JSON file.
        
        Args:
            project_id: The ID of the project to export.
            file_path: The file path to save the exported JSON.
            
        Returns:
            ServiceResult containing the project dictionary.
        """
        self.validate_id_format(project_id, "project")
        
        if self.should_use_database():
            project = await self.project_repo.get_by_project_id(project_id)
            if not project:
                return ServiceResult.error_result("Project not found", "NOT_FOUND")
            import json
            project_dict = project.dict(by_alias=True)
            if file_path:
                from pathlib import Path
                Path(file_path).write_text(json.dumps(project_dict, indent=2))
            return ServiceResult.success_result(project_dict)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")
    
    @handle_service_exceptions
    @log_service_operation
    async def import_project(self, file_path: str) -> ServiceResult[Project]:
        """
        Import a project from a JSON file.
        
        Args:
            file_path: The file path of the JSON file to import.
            
        Returns:
            ServiceResult containing the imported project.
        """
        if self.should_use_database():
            import json
            from pathlib import Path
            if not Path(file_path).exists():
                return ServiceResult.error_result("File not found", "NOT_FOUND")
            project_data = json.loads(Path(file_path).read_text())
            project = Project(**project_data)
            created_project = await self.project_repo.create_project(project)
            return ServiceResult.success_result(created_project)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")
    
    @handle_service_exceptions
    @log_service_operation
    async def export_test_suite(self, project_id: str, suite_id: str, file_path: str) -> ServiceResult[bool]:
        """
        Export a test suite to a JSON file.
        
        Args:
            project_id: The ID of the project containing the test suite.
            suite_id: The ID of the test suite to export.
            file_path: The file path to save the exported JSON.
            
        Returns:
            ServiceResult containing success status.
        """
        self.validate_id_format(project_id, "project")
        self.validate_id_format(suite_id, "suite")
        
        if self.should_use_database():
            project = await self.project_repo.get_by_project_id(project_id)
            if not project:
                return ServiceResult.error_result("Project not found", "NOT_FOUND")
            test_suite = project.get_test_suite(suite_id)
            if not test_suite:
                return ServiceResult.error_result("Test suite not found", "NOT_FOUND")
            import json
            from pathlib import Path
            suite_dict = test_suite.dict(by_alias=True)
            Path(file_path).write_text(json.dumps(suite_dict, indent=2))
            return ServiceResult.success_result(True)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")
    
    @handle_service_exceptions
    @log_service_operation
    async def import_test_suite(self, project_id: str, file_path: str) -> ServiceResult[TestSuite]:
        """
        Import a test suite from a JSON file into a project.
        
        Args:
            project_id: The ID of the project to import the test suite into.
            file_path: The file path of the JSON file to import.
            
        Returns:
            ServiceResult containing the imported test suite.
        """
        self.validate_id_format(project_id, "project")
        
        if self.should_use_database():
            import json
            from pathlib import Path
            if not Path(file_path).exists():
                return ServiceResult.error_result("File not found", "NOT_FOUND")
            suite_data = json.loads(Path(file_path).read_text())
            test_suite = TestSuite(**suite_data)
            project = await self.project_repo.get_by_project_id(project_id)
            if not project:
                return ServiceResult.error_result("Project not found", "NOT_FOUND")
            project.add_test_suite(test_suite)
            await self.project_repo.update_project(project)
            return ServiceResult.success_result(test_suite)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")
    
    @handle_service_exceptions
    @log_service_operation
    async def export_test_case(self, project_id: str, suite_id: str, test_id: str, file_path: str) -> ServiceResult[bool]:
        """
        Export a test case to a JSON file.
        
        Args:
            project_id: The ID of the project containing the test case.
            suite_id: The ID of the test suite containing the test case.
            test_id: The ID of the test case to export.
            file_path: The file path to save the exported JSON.
            
        Returns:
            ServiceResult containing success status.
        """
        self.validate_id_format(project_id, "project")
        self.validate_id_format(suite_id, "suite")
        self.validate_id_format(test_id, "test")
        
        if self.should_use_database():
            project = await self.project_repo.get_by_project_id(project_id)
            if not project:
                return ServiceResult.error_result("Project not found", "NOT_FOUND")
            test_suite = project.get_test_suite(suite_id)
            if not test_suite:
                return ServiceResult.error_result("Test suite not found", "NOT_FOUND")
            test_case = test_suite.get_test_case(test_id)
            if not test_case:
                return ServiceResult.error_result("Test case not found", "NOT_FOUND")
            import json
            from pathlib import Path
            case_dict = test_case.dict(by_alias=True)
            Path(file_path).write_text(json.dumps(case_dict, indent=2))
            return ServiceResult.success_result(True)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")
    
    @handle_service_exceptions
    @log_service_operation
    async def import_test_case(self, project_id: str, suite_id: str, file_path: str) -> ServiceResult[TestCase]:
        """
        Import a test case from a JSON file into a test suite.
        
        Args:
            project_id: The ID of the project containing the test suite.
            suite_id: The ID of the test suite to import the test case into.
            file_path: The file path of the JSON file to import.
            
        Returns:
            ServiceResult containing the imported test case.
        """
        self.validate_id_format(project_id, "project")
        self.validate_id_format(suite_id, "suite")
        
        if self.should_use_database():
            import json
            from pathlib import Path
            if not Path(file_path).exists():
                return ServiceResult.error_result("File not found", "NOT_FOUND")
            case_data = json.loads(Path(file_path).read_text())
            test_case = TestCase(**case_data)
            project = await self.project_repo.get_by_project_id(project_id)
            if not project:
                return ServiceResult.error_result("Project not found", "NOT_FOUND")
            test_suite = project.get_test_suite(suite_id)
            if not test_suite:
                return ServiceResult.error_result("Test suite not found", "NOT_FOUND")
            test_suite.add_test_case(test_case)
            await self.project_repo.update_project(project)
            return ServiceResult.success_result(test_case)
        else:
            return ServiceResult.error_result("Operation not supported in legacy mode", "NOT_SUPPORTED")
    
    @handle_service_exceptions
    @log_service_operation
    async def remove_history_reference(self, project_id: str, suite_id: str, test_id: str, history_path: str) -> ServiceResult[bool]:
        """Remove a history file reference from a test case (DB), fallback to legacy."""
        import os
        self.validate_id_format(project_id, "project")
        normalized_target = os.path.normpath(history_path)

        if self.should_use_database():
            try:
                # Direct MongoDB update to avoid Beanie model instantiation
                coll = await self.project_repo.collection
                update_result = await coll.update_one(
                    {"project_id": project_id},
                    {"$pull": {f"test_suites.{suite_id}.test_cases.{test_id}.history_files": normalized_target}}
                )
                if update_result.modified_count > 0:
                    return ServiceResult.success_result(True)
                # If nothing modified, continue to fallback
            except Exception as exc:
                self.logger.warning(f"Direct DB pull failed, will fallback to document update: {exc}")

        # Legacy or fallback using ProjectManagerService JSON update
        try:
            from src.utilities.project_manager_service import ProjectManagerService
            pm = ProjectManagerService()
            proj = pm.get_project(project_id)
            if proj:
                suite = proj.get_test_suite(suite_id)
                if suite:
                    case = suite.get_test_case(test_id)
                    if case and getattr(case, "history_files", None):
                        case.history_files = [h for h in case.history_files if os.path.normpath(h) != normalized_target]
                        pm.save_project(proj)
                        return ServiceResult.success_result(True)
        except Exception as exc:
            self.logger.warning(f"Legacy fallback failed in remove_history_reference: {exc}")

        return ServiceResult.error_result("Reference not removed", "NOT_MODIFIED")
    
    @log_service_operation
    @handle_service_exceptions
    async def update_complete_project(
        self,
        project_id: str,
        project_data: Dict[str, Any]
    ) -> ServiceResult[Optional[Project]]:
        """
        Update an entire project document including test suites and test cases.
        
        Args:
            project_id: Project identifier
            project_data: Complete project data dictionary
            
        Returns:
            ServiceResult containing the updated project
        """
        self.validate_id_format(project_id, "project")
        
        # Get existing project to verify it exists
        get_result = await self.get_project(project_id)
        if not get_result.success:
            return get_result
        
        if not get_result.data:
            return ServiceResult.error_result("Project not found", "NOT_FOUND")
        
        try:
            # Create a project model from the data
            updated_project = Project(**project_data)
            
            # Set the project_id to ensure it matches
            updated_project.project_id = project_id
            
            # Save complete project
            if self.should_use_database():
                result = await self.project_repo.update_project(updated_project)
                return ServiceResult.success_result(result)
            else:
                # Legacy mode
                legacy_project = self._convert_to_legacy_project(updated_project)
                success = self._save_legacy_project(legacy_project)
                return ServiceResult.success_result(updated_project) if success else ServiceResult.error_result("Failed to save project", "SAVE_ERROR")
        except Exception as e:
            logger.error(f"Error updating complete project: {str(e)}")
            return ServiceResult.exception_result(e)