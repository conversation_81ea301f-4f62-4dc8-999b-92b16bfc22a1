"""
Organization Service for QAK Multi-Tenant System

Handles organization management, member management, and role assignments.
"""

import logging
from typing import Optional, List, Dict, Any
from datetime import datetime

from src.database.models.organization import Organization, PlanType
from src.database.models.user import User
from src.database.models.role import Role, RoleName
from src.database.models.organization_member import OrganizationMember
from src.database.repositories.organization_repository import OrganizationRepository
from src.database.repositories.user_repository import UserRepository
from src.database.repositories.organization_member_repository import OrganizationMemberRepository
from src.database.exceptions import DatabaseError, DocumentNotFoundError

logger = logging.getLogger(__name__)


class OrganizationService:
    """Service for organization management operations."""
    
    def __init__(self):
        self.org_repo = OrganizationRepository()
        self.user_repo = UserRepository()
        self.member_repo = OrganizationMemberRepository()
    
    async def create_organization(self, name: str, created_by: str, description: str = "") -> Organization:
        """Create a new organization."""
        try:
            # Verify the creator exists
            creator = await self.user_repo.get_by_id(created_by)
            if not creator:
                raise DocumentNotFoundError(f"Creator user not found: {created_by}")
            
            # Create organization
            organization = Organization(
                name=name,
                description=description,
                plan_type=PlanType.FREE
            )
            organization = await self.org_repo.create(organization)
            
            # Get ORG_ADMIN role
            admin_role = await Role.find_one(Role.name == RoleName.ORG_ADMIN)
            if not admin_role:
                # Create ORG_ADMIN role if it doesn't exist
                admin_role = Role(
                    name=RoleName.ORG_ADMIN,
                    description="Organization administrator with management permissions",
                    permissions=["read", "write", "delete", "admin"]
                )
                await admin_role.insert()
            
            # Add creator as organization admin
            membership = OrganizationMember(
                user_id=created_by,
                org_id=organization.org_id,
                role_id=admin_role.role_id,
                invited_by=created_by
            )
            await self.member_repo.create(membership)
            
            logger.info(f"Created organization: {organization.org_id} by user: {created_by}")
            return organization
            
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to create organization: {e}")
            raise DatabaseError(f"Failed to create organization: {e}")
    
    async def get_organization(self, org_id: str) -> Optional[Organization]:
        """Get organization by ID."""
        try:
            organization = await self.org_repo.get_by_id(org_id)
            return organization
        except Exception as e:
            logger.error(f"Failed to get organization {org_id}: {e}")
            raise DatabaseError(f"Failed to get organization: {e}")
    
    async def update_organization(self, org_id: str, data: Dict[str, Any]) -> Organization:
        """Update organization information."""
        try:
            organization = await self.org_repo.get_by_id(org_id)
            if not organization:
                raise DocumentNotFoundError(f"Organization not found: {org_id}")
            
            # Update allowed fields
            if "name" in data:
                organization.name = data["name"].strip()
            
            if "description" in data:
                organization.description = data["description"].strip()
            
            if "plan_type" in data:
                organization.plan_type = PlanType(data["plan_type"])
            
            # Update organization
            organization = await self.org_repo.update(organization)
            logger.info(f"Updated organization: {org_id}")
            return organization
            
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to update organization {org_id}: {e}")
            raise DatabaseError(f"Failed to update organization: {e}")
    
    async def add_member(self, org_id: str, email: str, role: str, invited_by: str) -> OrganizationMember:
        """Add a new member to an organization."""
        try:
            # Verify organization exists
            organization = await self.org_repo.get_by_id(org_id)
            if not organization:
                raise DocumentNotFoundError(f"Organization not found: {org_id}")
            
            # Verify inviter exists and is a member
            inviter_membership = await self.member_repo.find_by_user_and_org(invited_by, org_id)
            if not inviter_membership or not inviter_membership.is_active:
                raise ValueError("Inviter is not an active member of the organization")
            
            # Get user by email
            user = await self.user_repo.get_by_email(email)
            if not user:
                raise DocumentNotFoundError(f"User not found with email: {email}")
            
            # Check if user is already a member
            existing_membership = await self.member_repo.find_by_user_and_org(user.user_id, org_id)
            if existing_membership:
                if existing_membership.is_active:
                    raise ValueError("User is already an active member of this organization")
                else:
                    # Reactivate existing membership
                    existing_membership.activate()
                    existing_membership.invited_by = invited_by
                    return await self.member_repo.update(existing_membership)
            
            # Get role
            role_obj = await Role.find_one(Role.name == RoleName(role))
            if not role_obj:
                raise DocumentNotFoundError(f"Role not found: {role}")
            
            # Create membership
            membership = OrganizationMember(
                user_id=user.user_id,
                org_id=org_id,
                role_id=role_obj.role_id,
                invited_by=invited_by
            )
            membership = await self.member_repo.create(membership)
            
            logger.info(f"Added member {user.email} to organization {org_id}")
            return membership
            
        except (DocumentNotFoundError, ValueError):
            raise
        except Exception as e:
            logger.error(f"Failed to add member to organization {org_id}: {e}")
            raise DatabaseError(f"Failed to add member: {e}")
    
    async def remove_member(self, org_id: str, user_id: str) -> bool:
        """Remove a member from an organization."""
        try:
            # Verify organization exists
            organization = await self.org_repo.get_by_id(org_id)
            if not organization:
                raise DocumentNotFoundError(f"Organization not found: {org_id}")
            
            # Remove membership
            success = await self.member_repo.remove_user_from_org(user_id, org_id)
            
            if success:
                logger.info(f"Removed member {user_id} from organization {org_id}")
            
            return success
            
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to remove member {user_id} from organization {org_id}: {e}")
            raise DatabaseError(f"Failed to remove member: {e}")
    
    async def change_member_role(self, org_id: str, user_id: str, new_role: str) -> bool:
        """Change a member's role in an organization."""
        try:
            # Verify organization exists
            organization = await self.org_repo.get_by_id(org_id)
            if not organization:
                raise DocumentNotFoundError(f"Organization not found: {org_id}")
            
            # Get new role
            role_obj = await Role.find_one(Role.name == RoleName(new_role))
            if not role_obj:
                raise DocumentNotFoundError(f"Role not found: {new_role}")
            
            # Change role
            success = await self.member_repo.change_user_role(user_id, org_id, role_obj.role_id)
            
            if success:
                logger.info(f"Changed role for member {user_id} in organization {org_id} to {new_role}")
            
            return success
            
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to change member role in organization {org_id}: {e}")
            raise DatabaseError(f"Failed to change member role: {e}")
    
    async def get_organization_members(self, org_id: str, active_only: bool = True) -> List[Dict[str, Any]]:
        """Get all members of an organization with details."""
        try:
            memberships = await self.member_repo.get_org_members(org_id, active_only)
            
            members = []
            for membership in memberships:
                # Get user details
                user = await self.user_repo.get_by_id(membership.user_id)
                
                # Get role details
                role = await Role.find_one(Role.role_id == membership.role_id)
                
                if user:
                    member_data = {
                        "user": user,
                        "membership": membership,
                        "role": role,
                        "joined_at": membership.joined_at,
                        "is_active": membership.is_active
                    }
                    members.append(member_data)
            
            logger.info(f"Retrieved {len(members)} members for organization: {org_id}")
            return members
            
        except Exception as e:
            logger.error(f"Failed to get members for organization {org_id}: {e}")
            raise DatabaseError(f"Failed to get organization members: {e}")
    
    async def get_organization_stats(self, org_id: str) -> Dict[str, Any]:
        """Get organization statistics."""
        try:
            stats = await self.org_repo.get_organization_stats(org_id)
            return stats
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to get organization stats for {org_id}: {e}")
            raise DatabaseError(f"Failed to get organization stats: {e}")
    
    async def user_has_permission(self, user_id: str, org_id: str, permission: str) -> bool:
        """Check if user has a specific permission in an organization."""
        try:
            # Get user's role in organization
            role_id = await self.member_repo.get_user_role_in_org(user_id, org_id)
            if not role_id:
                return False
            
            # Get role and check permissions
            role = await Role.find_one(Role.role_id == role_id)
            if not role:
                return False
            
            return role.has_permission(permission)
            
        except Exception as e:
            logger.error(f"Failed to check permission for user {user_id} in org {org_id}: {e}")
            return False
    
    async def is_user_org_admin(self, user_id: str, org_id: str) -> bool:
        """Check if user is an organization admin."""
        try:
            role_id = await self.member_repo.get_user_role_in_org(user_id, org_id)
            if not role_id:
                return False
            
            role = await Role.find_one(Role.role_id == role_id)
            if not role:
                return False
            
            return role.name == RoleName.ORG_ADMIN or role.name == RoleName.ADMIN
            
        except Exception as e:
            logger.error(f"Failed to check admin status for user {user_id} in org {org_id}: {e}")
            return False
