"""
Role Model for QAK Multi-Tenant Authorization

Beanie ODM model for role-based access control in the multi-tenant system.
"""

from typing import List, Dict, Any
from datetime import datetime
from enum import Enum
from pydantic import Field, field_validator, ConfigDict
from beanie import Indexed, Document
import uuid


class RoleName(str, Enum):
    """Predefined system roles."""
    USER = "USER"
    ORG_ADMIN = "ORG_ADMIN"
    ADMIN = "ADMIN"


class Permission(str, Enum):
    """Permission types for resources."""
    READ = "read"
    WRITE = "write"
    DELETE = "delete"
    ADMIN = "admin"


class Role(Document):
    """Role model for authorization and permission management."""
    
    # Identifiers
    role_id: str = Indexed(str, unique=True, description="Unique role identifier")
    
    # Role Information
    name: Indexed(RoleName) = Field(..., description="Role name")
    description: str = Field(..., description="Role description")
    
    # Permissions
    permissions: List[Permission] = Field(default_factory=list, description="List of permissions for this role")
    
    # System Role Flag
    is_system_role: bool = Field(default=True, description="Whether this is a system-defined role")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Role creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    
    # Configuration
    model_config = ConfigDict(
        extra="ignore",
        str_strip_whitespace=True,
        validate_assignment=True
    )
    
    @field_validator('role_id', mode='before')
    @classmethod
    def generate_role_id(cls, v):
        """Generate a unique role ID if not provided."""
        if v is None or v == "":
            return str(uuid.uuid4())
        return v
    
    @field_validator('description')
    @classmethod
    def validate_description(cls, v):
        """Validate description is not empty."""
        if not v or not v.strip():
            raise ValueError("Role description cannot be empty")
        return v.strip()
    
    @field_validator('permissions')
    @classmethod
    def validate_permissions(cls, v):
        """Validate permissions list."""
        if not isinstance(v, list):
            return []
        # Remove duplicates while preserving order
        seen = set()
        unique_permissions = []
        for perm in v:
            if perm not in seen:
                seen.add(perm)
                unique_permissions.append(perm)
        return unique_permissions
    
    def has_permission(self, permission: Permission) -> bool:
        """Check if role has a specific permission."""
        return permission in self.permissions
    
    def add_permission(self, permission: Permission) -> None:
        """Add a permission to the role."""
        if permission not in self.permissions:
            self.permissions.append(permission)
            self.update_timestamp()
    
    def remove_permission(self, permission: Permission) -> None:
        """Remove a permission from the role."""
        if permission in self.permissions:
            self.permissions.remove(permission)
            self.update_timestamp()
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
    
    @classmethod
    def get_default_roles(cls) -> List[Dict[str, Any]]:
        """Get the default system roles configuration."""
        return [
            {
                "name": RoleName.USER,
                "description": "Regular user with basic permissions",
                "permissions": [Permission.READ, Permission.WRITE],
                "is_system_role": True
            },
            {
                "name": RoleName.ORG_ADMIN,
                "description": "Organization administrator with management permissions",
                "permissions": [Permission.READ, Permission.WRITE, Permission.DELETE, Permission.ADMIN],
                "is_system_role": True
            },
            {
                "name": RoleName.ADMIN,
                "description": "System administrator with full access",
                "permissions": [Permission.READ, Permission.WRITE, Permission.DELETE, Permission.ADMIN],
                "is_system_role": True
            }
        ]
    
    class Settings:
        """Beanie document settings."""
        name = "roles"
        indexes = [
            "role_id",
            "name",
            "is_system_role",
            [("name", 1), ("is_system_role", 1)],  # Compound index for role lookups
        ]
    
    def __str__(self) -> str:
        return f"Role(role_id={self.role_id}, name={self.name}, permissions={self.permissions})"
    
    def __repr__(self) -> str:
        return self.__str__()
