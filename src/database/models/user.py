"""
User Model for QAK Multi-Tenant Authentication

Beanie ODM model for user entities with authentication and profile information.
"""

from typing import Optional
from datetime import datetime
from pydantic import Field, field_validator, EmailStr, ConfigDict
from beanie import Indexed, Document
import uuid


class User(Document):
    """User model for authentication and profile management."""
    
    # Identifiers
    user_id: Indexed(str, unique=True) = Field(..., description="Unique user identifier")

    # Authentication
    email: Indexed(EmailStr, unique=True) = Field(..., description="User email address")
    password_hash: str = Field(..., description="Hashed password")
    
    # Profile Information
    full_name: str = Field(..., description="User's full name")
    
    # Status and Verification
    is_active: bool = Field(default=True, description="Whether the user account is active")
    email_verified: bool = Field(default=False, description="Whether the user's email is verified")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="User creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    last_login: Optional[datetime] = Field(default=None, description="Last login timestamp")
    
    # Configuration
    model_config = ConfigDict(
        extra="ignore",
        str_strip_whitespace=True,
        validate_assignment=True
    )
    
    @field_validator('user_id', mode='before')
    @classmethod
    def generate_user_id(cls, v):
        """Generate a unique user ID if not provided."""
        if v is None or v == "":
            return str(uuid.uuid4())
        return v
    
    @field_validator('email')
    @classmethod
    def normalize_email(cls, v):
        """Normalize email to lowercase."""
        if isinstance(v, str):
            return v.lower().strip()
        return v
    
    @field_validator('full_name')
    @classmethod
    def validate_full_name(cls, v):
        """Validate full name is not empty."""
        if not v or not v.strip():
            raise ValueError("Full name cannot be empty")
        return v.strip()
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
    
    class Settings:
        """Beanie document settings."""
        name = "users"
        indexes = [
            "user_id",
            "email", 
            "created_at",
            [("email", 1), ("is_active", 1)],  # Compound index for active user lookups
        ]
    
    def __str__(self) -> str:
        return f"User(user_id={self.user_id}, email={self.email}, full_name={self.full_name})"
    
    def __repr__(self) -> str:
        return self.__str__()
