"""
QAK Database Models

This package contains all Beanie ODM models for the QAK application.
"""

from .project import Project
from .execution import Execution
from .codegen_session import CodegenSession
from .artifact import Artifact
from .semantic_pattern import SemanticPattern, PatternContext
from .semantic_interaction import SemanticInteraction, TargetElement

# Multi-tenant authentication models
from .user import User
from .organization import Organization
from .role import Role
from .organization_member import OrganizationMember

# List of all document models for Beanie initialization
__all__ = [
    # Core application models
    "Project",
    "Execution",
    "CodegenSession",
    "Artifact",
    "SemanticPattern",
    "PatternContext",
    "SemanticInteraction",
    "TargetElement",
    # Authentication and multi-tenancy models
    "User",
    "Organization",
    "Role",
    "OrganizationMember",
]