"""
Codegen Session Model for QAK MongoDB Implementation

Beanie ODM model for browser automation and codegen sessions.
Converted to use Beanie Document directly with proper indexing.
"""

from typing import Dict, List, Optional, Any
from datetime import datetime
from enum import Enum
from beanie import Document, Indexed
from pydantic import Field, field_validator, ConfigDict
import uuid


class CodegenStatus(str, Enum):
    """Status values for codegen sessions."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TargetLanguage(str, Enum):
    """Supported target languages for code generation."""
    JAVASCRIPT = "javascript"
    TYPESCRIPT = "typescript"
    PYTHON = "python"
    JAVA = "java"
    CSHARP = "csharp"


class CodegenSession(Document):
    """Browser automation and code generation session model."""
    
    # Session identification - indexed for performance
    session_id: Indexed(str, unique=True) = Field(default_factory=lambda: str(uuid.uuid4()))
    name: Optional[str] = None
    description: Optional[str] = None

    # Multi-tenancy fields
    organization_id: Indexed(str) = Field(..., description="Organization this session belongs to")
    created_by: str = Field(..., description="User ID of who created this session")
    
    # Session status and timing - indexed for queries
    status: Indexed(str) = Field(default=CodegenStatus.PENDING.value)
    created_at: Indexed(datetime) = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)
    completed_at: Optional[datetime] = None
    
    # Code generation configuration
    target_language: Indexed(str) = Field(default=TargetLanguage.JAVASCRIPT.value)
    url: Optional[Indexed(str)] = None  # Indexed for URL-based queries
    command_used: Optional[str] = None
    
    # Generated content and artifacts
    generated_code: Optional[str] = None
    artifacts_path: Optional[str] = None
    
    # Error handling
    error_message: Optional[str] = None
    
    # Project integration
    project_integration: Optional[Dict[str, Any]] = None
    
    # Execution tracking
    execution_count: int = Field(default=0)
    
    # Browser configuration
    browser_config: Optional[Dict[str, Any]] = None
    automation_config: Optional[Dict[str, Any]] = None
    
    # Additional metadata
    metadata: Optional[Dict[str, Any]] = None
    
    # MongoDB document settings
    class Settings:
        name = "codegen_sessions"
        indexes = [
            "session_id",
            "organization_id",
            "created_by",
            "status",
            "created_at",
            "target_language",
            "url",
            [("organization_id", 1), ("created_at", -1)],  # Compound index for org sessions by date
            [("organization_id", 1), ("status", 1)],  # Compound index for org sessions by status
            [("created_by", 1), ("created_at", -1)],  # Compound index for user sessions by date
        ]
    
    model_config = ConfigDict(
        use_enum_values=True,
        json_encoders={
            datetime: lambda v: v.isoformat() if v else None
        },
        extra="ignore"
    )
    
    @field_validator('session_id')
    @classmethod
    def validate_session_id(cls, v):
        """Validate session_id format."""
        if not v or len(v.strip()) == 0:
            raise ValueError("session_id cannot be empty")
        return v.strip()
    
    @field_validator('status', mode='before')
    @classmethod
    def validate_status(cls, v):
        """Validate and convert status to enum value."""
        if isinstance(v, str):
            return CodegenStatus(v).value
        elif isinstance(v, CodegenStatus):
            return v.value
        return v
    
    @field_validator('target_language', mode='before')
    @classmethod
    def validate_target_language(cls, v):
        """Validate and convert target_language to enum value."""
        if isinstance(v, str):
            return TargetLanguage(v).value
        elif isinstance(v, TargetLanguage):
            return v.value
        return v
    
    @field_validator('url')
    @classmethod
    def validate_url(cls, v):
        """Basic URL validation."""
        if v and not v.startswith(('http://', 'https://')):
            raise ValueError("url must start with http:// or https://")
        return v

    @field_validator('organization_id')
    @classmethod
    def validate_organization_id(cls, v):
        """Validate organization_id is not empty."""
        if not v or not v.strip():
            raise ValueError("organization_id cannot be empty")
        return v.strip()

    @field_validator('created_by')
    @classmethod
    def validate_created_by(cls, v):
        """Validate created_by is not empty."""
        if not v or not v.strip():
            raise ValueError("created_by cannot be empty")
        return v.strip()
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.now()
    
    def start_session(self):
        """Mark session as started."""
        self.status = CodegenStatus.RUNNING
        self.update_timestamp()
    
    def complete_session(self, generated_code: Optional[str] = None):
        """Mark session as completed."""
        self.status = CodegenStatus.COMPLETED
        self.completed_at = datetime.now()
        if generated_code:
            self.generated_code = generated_code
        self.update_timestamp()
    
    def fail_session(self, error_message: str):
        """Mark session as failed with error message."""
        self.status = CodegenStatus.FAILED
        self.completed_at = datetime.now()
        self.error_message = error_message
        self.update_timestamp()
    
    def cancel_session(self):
        """Mark session as cancelled."""
        self.status = CodegenStatus.CANCELLED
        self.completed_at = datetime.now()
        self.update_timestamp()
    
    def increment_execution_count(self):
        """Increment the execution count for this session."""
        self.execution_count += 1
        self.update_timestamp()
    
    @property
    def duration_seconds(self) -> Optional[int]:
        """Calculate session duration in seconds."""
        if not self.completed_at:
            return None
        return int((self.completed_at - self.created_at).total_seconds())
    
    @property
    def is_active(self) -> bool:
        """Check if session is currently active."""
        return self.status in [CodegenStatus.PENDING, CodegenStatus.RUNNING]
    
    @property
    def is_completed(self) -> bool:
        """Check if session is completed successfully."""
        return self.status == CodegenStatus.COMPLETED
    
    @property
    def has_generated_code(self) -> bool:
        """Check if session has generated code."""
        return self.generated_code is not None and len(self.generated_code.strip()) > 0
    
    @classmethod
    def from_legacy_json(cls, json_data: Dict[str, Any], session_id: str = None) -> "CodegenSession":
        """Create CodegenSession from legacy JSON format."""
        
        # Use provided session_id or extract from data
        sess_id = session_id or json_data.get("session_id", str(uuid.uuid4()))
        
        # Handle datetime strings
        created_at = datetime.now()
        updated_at = datetime.now()
        completed_at = None
        
        if "created_at" in json_data and isinstance(json_data["created_at"], str):
            created_at = datetime.fromisoformat(json_data["created_at"].replace("Z", "+00:00"))
        if "updated_at" in json_data and isinstance(json_data["updated_at"], str):
            updated_at = datetime.fromisoformat(json_data["updated_at"].replace("Z", "+00:00"))
        if "completed_at" in json_data and json_data["completed_at"] and isinstance(json_data["completed_at"], str):
            completed_at = datetime.fromisoformat(json_data["completed_at"].replace("Z", "+00:00"))
        
        # Map status
        status = CodegenStatus.PENDING
        if "status" in json_data:
            try:
                status = CodegenStatus(json_data["status"])
            except ValueError:
                status = CodegenStatus.PENDING
        
        # Map target language
        target_language = TargetLanguage.JAVASCRIPT
        if "target_language" in json_data:
            try:
                target_language = TargetLanguage(json_data["target_language"])
            except ValueError:
                target_language = TargetLanguage.JAVASCRIPT
        
        return cls(
            session_id=sess_id,
            status=status,
            target_language=target_language,
            url=json_data.get("url") or json_data.get("target_url"),
            created_at=created_at,
            updated_at=updated_at,
            completed_at=completed_at,
            command_used=json_data.get("command_used"),
            error_message=json_data.get("error_message"),
            generated_code=json_data.get("generated_code"),
            project_integration=json_data.get("project_integration"),
            artifacts_path=json_data.get("artifacts_path"),
            browser_config=json_data.get("browser_config"),
            execution_count=json_data.get("execution_count", 0),
            metadata=json_data
        )
    
    def to_legacy_json(self) -> Dict[str, Any]:
        """Convert CodegenSession to legacy JSON format."""
        return {
            "session_id": self.session_id,
            "status": self.status.value,
            "target_language": self.target_language.value,
            "url": self.url,
            "target_url": self.url,  # Backward compatibility
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "completed_at": self.completed_at.isoformat() if self.completed_at else None,
            "command_used": self.command_used,
            "error_message": self.error_message,
            "generated_code": self.generated_code,
            "project_integration": self.project_integration,
            "artifacts_path": self.artifacts_path,
            "browser_config": self.browser_config,
            "execution_count": self.execution_count,
            "metadata": self.metadata or {}
        } 