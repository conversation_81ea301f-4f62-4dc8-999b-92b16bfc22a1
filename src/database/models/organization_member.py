"""
OrganizationMember Model for QAK Multi-Tenant System

Beanie ODM model for managing user memberships in organizations with roles.
"""

from typing import Optional
from datetime import datetime
from pydantic import Field, field_validator, ConfigDict
from beanie import Indexed, Document, Link
import uuid

# Import related models for type hints
from .user import User
from .organization import Organization
from .role import Role


class OrganizationMember(Document):
    """Organization member model for managing user-organization relationships."""
    
    # Identifiers
    member_id: Indexed(str, unique=True) = Field(..., description="Unique membership identifier")

    # Relationships (using string references to avoid circular imports)
    user_id: Indexed(str) = Field(..., description="Reference to User document")
    org_id: Indexed(str) = Field(..., description="Reference to Organization document")
    role_id: Indexed(str) = Field(..., description="Reference to Role document")
    
    # Membership Information
    joined_at: datetime = Field(default_factory=datetime.utcnow, description="When the user joined the organization")
    invited_by: Optional[str] = Field(default=None, description="User ID of who invited this member")
    is_active: bool = Field(default=True, description="Whether the membership is active")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Membership creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    
    # Configuration
    model_config = ConfigDict(
        extra="ignore",
        str_strip_whitespace=True,
        validate_assignment=True
    )
    
    @field_validator('member_id', mode='before')
    @classmethod
    def generate_member_id(cls, v):
        """Generate a unique member ID if not provided."""
        if v is None or v == "":
            return str(uuid.uuid4())
        return v
    
    @field_validator('user_id')
    @classmethod
    def validate_user_id(cls, v):
        """Validate user_id is not empty."""
        if not v or not v.strip():
            raise ValueError("User ID cannot be empty")
        return v.strip()
    
    @field_validator('org_id')
    @classmethod
    def validate_org_id(cls, v):
        """Validate org_id is not empty."""
        if not v or not v.strip():
            raise ValueError("Organization ID cannot be empty")
        return v.strip()
    
    @field_validator('role_id')
    @classmethod
    def validate_role_id(cls, v):
        """Validate role_id is not empty."""
        if not v or not v.strip():
            raise ValueError("Role ID cannot be empty")
        return v.strip()
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
    
    def deactivate(self):
        """Deactivate the membership."""
        self.is_active = False
        self.update_timestamp()
    
    def activate(self):
        """Activate the membership."""
        self.is_active = True
        self.update_timestamp()
    
    async def get_user(self) -> Optional[User]:
        """Get the associated user document."""
        return await User.get(self.user_id)
    
    async def get_organization(self) -> Optional[Organization]:
        """Get the associated organization document."""
        return await Organization.get(self.org_id)
    
    async def get_role(self) -> Optional[Role]:
        """Get the associated role document."""
        return await Role.get(self.role_id)
    
    class Settings:
        """Beanie document settings."""
        name = "organization_members"
        indexes = [
            "member_id",
            "user_id",
            "org_id",
            "role_id",
            [("user_id", 1), ("org_id", 1)],  # Unique compound index for user-org relationship
            [("org_id", 1), ("is_active", 1)],  # Compound index for active members in org
            [("user_id", 1), ("is_active", 1)],  # Compound index for active memberships of user
            [("role_id", 1), ("is_active", 1)],  # Compound index for role-based queries
        ]
    
    def __str__(self) -> str:
        return f"OrganizationMember(member_id={self.member_id}, user_id={self.user_id}, org_id={self.org_id}, role_id={self.role_id})"
    
    def __repr__(self) -> str:
        return self.__str__()
