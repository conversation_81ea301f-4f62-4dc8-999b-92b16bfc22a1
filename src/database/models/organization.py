"""
Organization Model for QAK Multi-Tenant System

Beanie ODM model for organization entities in the multi-tenant architecture.
"""

from typing import Optional
from datetime import datetime
from enum import Enum
from pydantic import Field, field_validator, ConfigDict
from beanie import Indexed, Document
import uuid
import re


class PlanType(str, Enum):
    """Organization plan types."""
    FREE = "free"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class Organization(Document):
    """Organization model for multi-tenant isolation."""
    
    # Identifiers
    org_id: Indexed(str, unique=True) = Field(..., description="Unique organization identifier")

    # Basic Information
    name: str = Field(..., description="Organization name")
    slug: Indexed(str, unique=True) = Field(..., description="URL-friendly organization identifier")
    description: str = Field(default="", description="Organization description")
    
    # Plan and Status
    plan_type: PlanType = Field(default=PlanType.FREE, description="Organization plan type")
    is_active: bool = Field(default=True, description="Whether the organization is active")
    
    # Timestamps
    created_at: datetime = Field(default_factory=datetime.utcnow, description="Organization creation timestamp")
    updated_at: datetime = Field(default_factory=datetime.utcnow, description="Last update timestamp")
    
    # Configuration
    model_config = ConfigDict(
        extra="ignore",
        str_strip_whitespace=True,
        validate_assignment=True
    )
    
    @field_validator('org_id', mode='before')
    @classmethod
    def generate_org_id(cls, v):
        """Generate a unique organization ID if not provided."""
        if v is None or v == "":
            return str(uuid.uuid4())
        return v
    
    @field_validator('name')
    @classmethod
    def validate_name(cls, v):
        """Validate organization name is not empty."""
        if not v or not v.strip():
            raise ValueError("Organization name cannot be empty")
        return v.strip()
    
    @field_validator('slug', mode='before')
    @classmethod
    def generate_slug(cls, v, info):
        """Generate a URL-friendly slug from name if not provided."""
        if v is None or v == "":
            # Get name from values if available
            name = info.data.get('name', '')
            if name:
                # Convert to lowercase, replace spaces and special chars with hyphens
                slug = re.sub(r'[^a-z0-9]+', '-', name.lower().strip())
                # Remove leading/trailing hyphens and multiple consecutive hyphens
                slug = re.sub(r'^-+|-+$', '', slug)
                slug = re.sub(r'-+', '-', slug)
                return slug
            return str(uuid.uuid4())[:8]  # Fallback to short UUID
        return v
    
    @field_validator('slug')
    @classmethod
    def validate_slug(cls, v):
        """Validate slug format."""
        if not v:
            raise ValueError("Slug cannot be empty")
        
        # Check slug format (alphanumeric and hyphens only)
        if not re.match(r'^[a-z0-9-]+$', v):
            raise ValueError("Slug can only contain lowercase letters, numbers, and hyphens")
        
        # Check length
        if len(v) < 2 or len(v) > 50:
            raise ValueError("Slug must be between 2 and 50 characters")
        
        return v
    
    def update_timestamp(self):
        """Update the updated_at timestamp."""
        self.updated_at = datetime.utcnow()
    
    class Settings:
        """Beanie document settings."""
        name = "organizations"
        indexes = [
            "org_id",
            "slug",
            "created_at",
            [("name", 1), ("is_active", 1)],  # Compound index for active organization lookups
            [("plan_type", 1), ("is_active", 1)],  # Compound index for plan-based queries
        ]
    
    def __str__(self) -> str:
        return f"Organization(org_id={self.org_id}, name={self.name}, slug={self.slug})"
    
    def __repr__(self) -> str:
        return self.__str__()
