"""
Codegen Repository for QAK MongoDB Implementation

Repository for codegen session operations with specialized queries,
analytics, and legacy data migration support.
"""

from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from beanie import PydanticObjectId
from beanie.operators import And, Or, In
from pymongo import DESCENDING, ASCENDING

from .base import BaseRepository
from ..models.codegen_session import CodegenSession, CodegenStatus, TargetLanguage


class CodegenSessionRepository(BaseRepository[CodegenSession]):
    """
    Repository for codegen session document operations.
    
    Provides specialized queries for sessions, analytics, and legacy data migration.
    """
    
    document_model = CodegenSession
    
    def __init__(self):
        """Initialize repository."""
        super().__init__("codegen_sessions")

    def _status_filter(self, status: CodegenStatus) -> dict:
        """Helper method to create status filter for queries."""
        return {"status": status.value if hasattr(status, 'value') else status}
    
    def to_document(self, model: CodegenSession) -> Dict[str, Any]:
        """Convert CodegenSession model to MongoDB document."""
        return model.model_dump()

    def from_document(self, document: Dict[str, Any]) -> CodegenSession:
        """Convert MongoDB document to CodegenSession model."""
        return CodegenSession(**document)

    def get_document_id(self, model: CodegenSession) -> str:
        """Get the document ID from CodegenSession model."""
        return str(model.id) if model.id else ""
    
    async def get_by_session_id(self, session_id: str) -> Optional[CodegenSession]:
        """
        Get session by session_id.
        
        Args:
            session_id: Session ID to search for
            
        Returns:
            CodegenSession: Found document or None
        """
        return await CodegenSession.find_one(
            CodegenSession.session_id == session_id
        )
    
    async def find_by_status(self, status: CodegenStatus) -> List[CodegenSession]:
        """
        Find sessions by status.
        
        Args:
            status: Status to filter by
            
        Returns:
            List of CodegenSession
        """
        return await CodegenSession.find(
            {"status": status.value if hasattr(status, 'value') else status}
        ).sort([("created_at", DESCENDING)]).to_list()
    
    async def find_active_sessions(self) -> List[CodegenSession]:
        """
        Find all active (pending or running) sessions.
        
        Returns:
            List of active CodegenSession
        """
        return await CodegenSession.find(
            {"status": {"$in": [CodegenStatus.PENDING.value, CodegenStatus.RUNNING.value]}}
        ).sort([("created_at", ASCENDING)]).to_list()
    
    async def find_completed_sessions(self, limit: int = 50) -> List[CodegenSession]:
        """
        Find completed sessions.
        
        Args:
            limit: Maximum number of results
            
        Returns:
            List of completed CodegenSession
        """
        return await CodegenSession.find(
            {"status": CodegenStatus.COMPLETED.value}
        ).sort([("completed_at", DESCENDING)]).limit(limit).to_list()
    
    async def find_failed_sessions(self, limit: int = 50) -> List[CodegenSession]:
        """
        Find failed sessions.
        
        Args:
            limit: Maximum number of results
            
        Returns:
            List of failed CodegenSession
        """
        return await CodegenSession.find(
            {"status": CodegenStatus.FAILED.value}
        ).sort([("created_at", DESCENDING)]).limit(limit).to_list()
    
    async def find_by_language(self, language: TargetLanguage) -> List[CodegenSession]:
        """
        Find sessions by target language.
        
        Args:
            language: Target language to filter by
            
        Returns:
            List of CodegenSession
        """
        return await CodegenSession.find(
            CodegenSession.target_language == language
        ).sort([("created_at", DESCENDING)]).to_list()
    
    async def find_by_url(self, url: str) -> List[CodegenSession]:
        """
        Find sessions by target URL.
        
        Args:
            url: URL to search for
            
        Returns:
            List of CodegenSession
        """
        return await CodegenSession.find(
            CodegenSession.url == url
        ).sort([("created_at", DESCENDING)]).to_list()
    
    async def find_by_url_pattern(self, url_pattern: str) -> List[CodegenSession]:
        """
        Find sessions by URL pattern (regex).
        
        Args:
            url_pattern: Regex pattern to match URLs
            
        Returns:
            List of matching CodegenSession
        """
        return await CodegenSession.find(
            CodegenSession.url.regex(url_pattern, "i")
        ).sort([("created_at", DESCENDING)]).to_list()
    
    async def find_sessions_in_range(
        self,
        start_date: datetime,
        end_date: datetime,
        status: Optional[CodegenStatus] = None,
        language: Optional[TargetLanguage] = None,
        limit: int = 50,
        skip: int = 0
    ) -> List[CodegenSession]:
        """
        Find sessions within a date range with optional filters.
        
        Args:
            start_date: Start date
            end_date: End date
            status: Optional status filter
            language: Optional language filter
            limit: Maximum number of results
            skip: Number of results to skip
            
        Returns:
            List of CodegenSession
        """
        query = And(
            CodegenSession.created_at >= start_date,
            CodegenSession.created_at <= end_date
        )
        
        if status:
            query = And(query, CodegenSession.status == status)
        
        if language:
            query = And(query, CodegenSession.target_language == language)
        
        return await CodegenSession.find(query)\
            .sort([("created_at", DESCENDING)])\
            .limit(limit)\
            .skip(skip)\
            .to_list()
    
    async def find_recent_sessions(
        self,
        hours: int = 24,
        status: Optional[CodegenStatus] = None,
        limit: int = 50
    ) -> List[CodegenSession]:
        """
        Find recent sessions within specified hours.
        
        Args:
            hours: Number of hours to look back
            status: Optional status filter
            limit: Maximum number of results
            
        Returns:
            List of recent CodegenSession
        """
        cutoff_time = datetime.now() - timedelta(hours=hours)
        query = CodegenSession.created_at >= cutoff_time
        
        if status:
            query = And(query, CodegenSession.status == status)
        
        return await CodegenSession.find(query)\
            .sort([("created_at", DESCENDING)])\
            .limit(limit)\
            .to_list()
    
    async def find_sessions_today(
        self,
        status: Optional[CodegenStatus] = None
    ) -> List[CodegenSession]:
        """
        Find sessions created today.
        
        Args:
            status: Optional status filter
            
        Returns:
            List of today's CodegenSession
        """
        today_start = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        tomorrow_start = today_start + timedelta(days=1)
        
        query = And(
            CodegenSession.created_at >= today_start,
            CodegenSession.created_at < tomorrow_start
        )
        
        if status:
            query = And(query, CodegenSession.status == status)
        
        return await CodegenSession.find(query)\
            .sort([("created_at", DESCENDING)])\
            .to_list()
    
    async def get_session_statistics(self, days: int = 30) -> Dict[str, Any]:
        """
        Get session statistics for the specified period.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Dict with session statistics
        """
        since = datetime.now() - timedelta(days=days)
        
        # Aggregation pipeline
        pipeline = [
            {"$match": {"created_at": {"$gte": since}}},
            {
                "$group": {
                    "_id": "$status",
                    "count": {"$sum": 1}
                }
            }
        ]
        
        results = await CodegenSession.aggregate(pipeline).to_list()
        
        # Format results
        stats = {status.value: 0 for status in CodegenStatus}
        total_sessions = 0
        for res in results:
            if res["_id"] in stats:
                stats[res["_id"]] = res["count"]
                total_sessions += res["count"]
        
        stats["total_sessions"] = total_sessions
        return stats
    
    async def get_language_statistics(self, days: int = 30) -> List[Dict[str, Any]]:
        """
        Get statistics on target language usage.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            List of dicts with language and count
        """
        since = datetime.now() - timedelta(days=days)
        
        pipeline = [
            {"$match": {"created_at": {"$gte": since}}},
            {
                "$group": {
                    "_id": "$target_language",
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"count": DESCENDING}},
            {
                "$project": {
                    "language": "$_id",
                    "count": 1,
                    "_id": 0
                }
            }
        ]
        
        return await CodegenSession.aggregate(pipeline).to_list()
    
    async def get_daily_session_counts(self, days: int = 30) -> List[Dict[str, Any]]:
        """
        Get daily session counts for the specified period.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            List of dicts with date and session count
        """
        since = datetime.now() - timedelta(days=days)
        
        pipeline = [
            {"$match": {"created_at": {"$gte": since}}},
            {
                "$group": {
                    "_id": {
                        "$dateToString": {
                            "format": "%Y-%m-%d",
                            "date": "$created_at"
                        }
                    },
                    "count": {"$sum": 1}
                }
            },
            {"$sort": {"_id": ASCENDING}},
            {
                "$project": {
                    "date": "$_id",
                    "count": 1,
                    "_id": 0
                }
            }
        ]
        
        return await CodegenSession.aggregate(pipeline).to_list()
    
    async def cleanup_old_sessions(
        self,
        days: int = 90,
        dry_run: bool = True
    ) -> Dict[str, Any]:
        """
        Delete old, non-completed sessions.
        
        Args:
            days: Age in days for sessions to be considered old
            dry_run: If True, only count sessions to be deleted
            
        Returns:
            Dict with cleanup results
        """
        cutoff_date = datetime.now() - timedelta(days=days)
        
        query = And(
            CodegenSession.created_at < cutoff_date,
            CodegenSession.status != CodegenStatus.COMPLETED
        )
        
        if dry_run:
            count = await CodegenSession.find(query).count()
            return {"dry_run": True, "sessions_to_delete": count}
        
        delete_result = await CodegenSession.find(query).delete()
        return {
            "dry_run": False,
            "deleted_count": delete_result.deleted_count
        }
    
    async def get_sessions_with_generated_code(self, limit: int = 50) -> List[CodegenSession]:
        """
        Get sessions that have generated code.
        
        Args:
            limit: Maximum number of results
            
        Returns:
            List of CodegenSession with generated code
        """
        return await CodegenSession.find(
            CodegenSession.generated_code != None,
            CodegenSession.generated_code != ""
        ).sort([("created_at", DESCENDING)]).limit(limit).to_list()
    
    async def update_execution_count(self, session_id: str) -> Optional[CodegenSession]:
        """
        Increment the execution count for a session.
        
        Args:
            session_id: ID of the session to update
            
        Returns:
            Updated CodegenSession
        """
        return await CodegenSession.find_one(
            CodegenSession.session_id == session_id
        ).update({"$inc": {"execution_count": 1}})
    
    async def update_session_status(
        self,
        session_id: str,
        status: CodegenStatus,
        error_message: Optional[str] = None,
        generated_code: Optional[str] = None
    ) -> Optional[CodegenSession]:
        """
        Update the status and other details of a session.
        
        Args:
            session_id: ID of the session to update
            status: New status for the session
            error_message: Optional error message if failed
            generated_code: Optional generated code if completed
            
        Returns:
            Updated CodegenSession or None if not found
        """
        update_data = {
            "status": status,
            "updated_at": datetime.now()
        }
        
        if status in [CodegenStatus.COMPLETED, CodegenStatus.FAILED, CodegenStatus.CANCELLED]:
            update_data["completed_at"] = datetime.now()
        
        if status == CodegenStatus.COMPLETED and generated_code:
            update_data["generated_code"] = generated_code
        
        if status == CodegenStatus.FAILED and error_message:
            update_data["error_message"] = error_message
        
        updated_doc = await CodegenSession.find_one(
            CodegenSession.session_id == session_id
        ).update({"$set": update_data})
        
        return updated_doc


class CodegenMigrationService:
    """Service to handle migration of legacy codegen session data."""
    
    def __init__(self, session_repo: CodegenSessionRepository):
        self.repo = session_repo
    
    async def migrate_legacy_session(self, json_data: Dict[str, Any]) -> CodegenSession:
        """
        Migrate a single legacy session from JSON data.
        
        Args:
            json_data: Dictionary from legacy JSON file
            
        Returns:
            CodegenSession: Saved document
        """
        doc = CodegenSession.from_legacy_json(json_data)
        
        # Check if already exists
        existing = await self.repo.get_by_session_id(doc.session_id)
        if existing:
            # For this migration, we assume legacy data is authoritative and overwrite
            # In other scenarios, you might merge or skip
            for key, value in doc.dict(exclude_unset=True).items():
                setattr(existing, key, value)
            await existing.save()
            return existing
        else:
            return await self.repo.create(doc)
    
    async def migrate_sessions_batch(self, sessions_data: List[Dict[str, Any]]) -> List[CodegenSession]:
        """
        Migrate a batch of legacy sessions.
        
        Args:
            sessions_data: List of session data dictionaries
            
        Returns:
            List of saved CodegenSession documents
        """
        # This can be optimized with bulk operations
        migrated_docs = []
        for data in sessions_data:
            try:
                doc = await self.migrate_legacy_session(data)
                migrated_docs.append(doc)
            except Exception as e:
                session_id = data.get("session_id", "unknown")
                # Log error and continue with the batch
                logger.error(f"Failed to migrate session {session_id}: {e}", exc_info=True)
        return migrated_docs
    
    async def validate_migration(self, legacy_session_count: int) -> Dict[str, Any]:
        """
        Validate migration results.
        
        Args:
            legacy_session_count: Expected number of sessions
            
        Returns:
            Dict with validation results
        """
        # Count migrated documents
        session_count = await self.repo.count({})
        
        return {
            "expected": legacy_session_count,
            "migrated": session_count,
            "success": session_count >= legacy_session_count,
            "migration_complete": session_count >= legacy_session_count
        } 