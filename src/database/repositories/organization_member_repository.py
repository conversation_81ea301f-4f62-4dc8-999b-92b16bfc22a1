"""
OrganizationMember Repository for QAK Multi-Tenant System

Repository for managing user-organization relationships using Beanie ODM.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import logging

from src.database.models.organization_member import OrganizationMember
from src.database.models.user import User
from src.database.models.organization import Organization
from src.database.models.role import Role
from src.database.exceptions import DocumentNotFoundError, DatabaseError

logger = logging.getLogger(__name__)


class OrganizationMemberRepository:
    """Repository for OrganizationMember model operations."""
    
    async def create(self, member: OrganizationMember) -> OrganizationMember:
        """Create a new organization membership."""
        try:
            member.created_at = datetime.utcnow()
            member.updated_at = datetime.utcnow()
            await member.insert()
            logger.info(f"Created organization membership: {member.member_id}")
            return member
        except Exception as e:
            logger.error(f"Failed to create organization membership: {e}")
            raise DatabaseError(f"Failed to create organization membership: {e}")
    
    async def get_by_id(self, member_id: str) -> Optional[OrganizationMember]:
        """Get organization membership by ID."""
        try:
            member = await OrganizationMember.find_one(OrganizationMember.member_id == member_id)
            return member
        except Exception as e:
            logger.error(f"Failed to get organization membership by ID {member_id}: {e}")
            raise DatabaseError(f"Failed to get organization membership: {e}")
    
    async def find_by_user_and_org(self, user_id: str, org_id: str) -> Optional[OrganizationMember]:
        """Find membership by user and organization."""
        try:
            member = await OrganizationMember.find_one(
                OrganizationMember.user_id == user_id,
                OrganizationMember.org_id == org_id
            )
            return member
        except Exception as e:
            logger.error(f"Failed to find membership for user {user_id} in org {org_id}: {e}")
            raise DatabaseError(f"Failed to find organization membership: {e}")
    
    async def get_user_organizations(self, user_id: str, active_only: bool = True) -> List[OrganizationMember]:
        """Get all organizations for a user."""
        try:
            query = [OrganizationMember.user_id == user_id]
            if active_only:
                query.append(OrganizationMember.is_active == True)
            
            memberships = await OrganizationMember.find(*query).to_list()
            return memberships
        except Exception as e:
            logger.error(f"Failed to get organizations for user {user_id}: {e}")
            raise DatabaseError(f"Failed to get user organizations: {e}")
    
    async def get_org_members(self, org_id: str, active_only: bool = True) -> List[OrganizationMember]:
        """Get all members of an organization."""
        try:
            query = [OrganizationMember.org_id == org_id]
            if active_only:
                query.append(OrganizationMember.is_active == True)
            
            memberships = await OrganizationMember.find(*query).to_list()
            return memberships
        except Exception as e:
            logger.error(f"Failed to get members for organization {org_id}: {e}")
            raise DatabaseError(f"Failed to get organization members: {e}")
    
    async def update(self, member: OrganizationMember) -> OrganizationMember:
        """Update an existing organization membership."""
        try:
            member.update_timestamp()
            await member.save()
            logger.info(f"Updated organization membership: {member.member_id}")
            return member
        except Exception as e:
            logger.error(f"Failed to update organization membership {member.member_id}: {e}")
            raise DatabaseError(f"Failed to update organization membership: {e}")
    
    async def delete(self, member_id: str) -> bool:
        """Delete an organization membership by ID."""
        try:
            member = await self.get_by_id(member_id)
            if not member:
                raise DocumentNotFoundError(f"Organization membership not found: {member_id}")
            
            await member.delete()
            logger.info(f"Deleted organization membership: {member_id}")
            return True
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to delete organization membership {member_id}: {e}")
            raise DatabaseError(f"Failed to delete organization membership: {e}")
    
    async def remove_user_from_org(self, user_id: str, org_id: str) -> bool:
        """Remove a user from an organization."""
        try:
            member = await self.find_by_user_and_org(user_id, org_id)
            if not member:
                raise DocumentNotFoundError(f"Membership not found for user {user_id} in org {org_id}")
            
            await member.delete()
            logger.info(f"Removed user {user_id} from organization {org_id}")
            return True
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to remove user {user_id} from org {org_id}: {e}")
            raise DatabaseError(f"Failed to remove user from organization: {e}")
    
    async def deactivate_membership(self, user_id: str, org_id: str) -> bool:
        """Deactivate a user's membership in an organization."""
        try:
            member = await self.find_by_user_and_org(user_id, org_id)
            if not member:
                raise DocumentNotFoundError(f"Membership not found for user {user_id} in org {org_id}")
            
            member.deactivate()
            await member.save()
            
            logger.info(f"Deactivated membership for user {user_id} in org {org_id}")
            return True
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to deactivate membership for user {user_id} in org {org_id}: {e}")
            raise DatabaseError(f"Failed to deactivate membership: {e}")
    
    async def activate_membership(self, user_id: str, org_id: str) -> bool:
        """Activate a user's membership in an organization."""
        try:
            member = await self.find_by_user_and_org(user_id, org_id)
            if not member:
                raise DocumentNotFoundError(f"Membership not found for user {user_id} in org {org_id}")
            
            member.activate()
            await member.save()
            
            logger.info(f"Activated membership for user {user_id} in org {org_id}")
            return True
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to activate membership for user {user_id} in org {org_id}: {e}")
            raise DatabaseError(f"Failed to activate membership: {e}")
    
    async def change_user_role(self, user_id: str, org_id: str, new_role_id: str) -> bool:
        """Change a user's role in an organization."""
        try:
            member = await self.find_by_user_and_org(user_id, org_id)
            if not member:
                raise DocumentNotFoundError(f"Membership not found for user {user_id} in org {org_id}")
            
            member.role_id = new_role_id
            member.update_timestamp()
            await member.save()
            
            logger.info(f"Changed role for user {user_id} in org {org_id} to {new_role_id}")
            return True
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to change role for user {user_id} in org {org_id}: {e}")
            raise DatabaseError(f"Failed to change user role: {e}")
    
    async def is_user_member(self, user_id: str, org_id: str, active_only: bool = True) -> bool:
        """Check if user is a member of an organization."""
        try:
            query = [
                OrganizationMember.user_id == user_id,
                OrganizationMember.org_id == org_id
            ]
            if active_only:
                query.append(OrganizationMember.is_active == True)
            
            member = await OrganizationMember.find_one(*query)
            return member is not None
        except Exception as e:
            logger.error(f"Failed to check membership for user {user_id} in org {org_id}: {e}")
            raise DatabaseError(f"Failed to check membership: {e}")
    
    async def get_user_role_in_org(self, user_id: str, org_id: str) -> Optional[str]:
        """Get user's role ID in an organization."""
        try:
            member = await self.find_by_user_and_org(user_id, org_id)
            if member and member.is_active:
                return member.role_id
            return None
        except Exception as e:
            logger.error(f"Failed to get user role for {user_id} in org {org_id}: {e}")
            raise DatabaseError(f"Failed to get user role: {e}")
    
    async def count_org_members(self, org_id: str, active_only: bool = True) -> int:
        """Count members in an organization."""
        try:
            query = [OrganizationMember.org_id == org_id]
            if active_only:
                query.append(OrganizationMember.is_active == True)
            
            count = await OrganizationMember.find(*query).count()
            return count
        except Exception as e:
            logger.error(f"Failed to count members for org {org_id}: {e}")
            raise DatabaseError(f"Failed to count organization members: {e}")
