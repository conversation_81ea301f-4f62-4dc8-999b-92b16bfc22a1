"""
Organization Repository for QAK Multi-Tenant System

Repository for organization-specific database operations using Beanie <PERSON>DM.
"""

from typing import Optional, List, Dict, Any
from datetime import datetime
import logging

from src.database.models.organization import Organization
from src.database.exceptions import DocumentNotFoundError, DatabaseError

logger = logging.getLogger(__name__)


class OrganizationRepository:
    """Repository for Organization model operations."""
    
    async def create(self, organization: Organization) -> Organization:
        """Create a new organization."""
        try:
            organization.created_at = datetime.utcnow()
            organization.updated_at = datetime.utcnow()
            await organization.insert()
            logger.info(f"Created organization: {organization.org_id}")
            return organization
        except Exception as e:
            logger.error(f"Failed to create organization: {e}")
            raise DatabaseError(f"Failed to create organization: {e}")
    
    async def get_by_id(self, org_id: str) -> Optional[Organization]:
        """Get organization by ID."""
        try:
            organization = await Organization.find_one(Organization.org_id == org_id)
            return organization
        except Exception as e:
            logger.error(f"Failed to get organization by ID {org_id}: {e}")
            raise DatabaseError(f"Failed to get organization: {e}")
    
    async def get_by_slug(self, slug: str) -> Optional[Organization]:
        """Get organization by slug."""
        try:
            organization = await Organization.find_one(Organization.slug == slug)
            return organization
        except Exception as e:
            logger.error(f"Failed to get organization by slug {slug}: {e}")
            raise DatabaseError(f"Failed to get organization: {e}")
    
    async def update(self, organization: Organization) -> Organization:
        """Update an existing organization."""
        try:
            organization.update_timestamp()
            await organization.save()
            logger.info(f"Updated organization: {organization.org_id}")
            return organization
        except Exception as e:
            logger.error(f"Failed to update organization {organization.org_id}: {e}")
            raise DatabaseError(f"Failed to update organization: {e}")
    
    async def delete(self, org_id: str) -> bool:
        """Delete an organization by ID."""
        try:
            organization = await self.get_by_id(org_id)
            if not organization:
                raise DocumentNotFoundError(f"Organization not found: {org_id}")
            
            await organization.delete()
            logger.info(f"Deleted organization: {org_id}")
            return True
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to delete organization {org_id}: {e}")
            raise DatabaseError(f"Failed to delete organization: {e}")
    
    async def find_active_organizations(self, limit: int = 100, skip: int = 0) -> List[Organization]:
        """Find active organizations with pagination."""
        try:
            organizations = await Organization.find(
                Organization.is_active == True
            ).skip(skip).limit(limit).to_list()
            return organizations
        except Exception as e:
            logger.error(f"Failed to find active organizations: {e}")
            raise DatabaseError(f"Failed to find active organizations: {e}")
    
    async def find_by_name_pattern(self, name_pattern: str, limit: int = 50) -> List[Organization]:
        """Find organizations by name pattern (case-insensitive)."""
        try:
            # Using regex for case-insensitive search
            import re
            pattern = re.compile(name_pattern, re.IGNORECASE)
            organizations = await Organization.find(
                {"name": {"$regex": pattern}}
            ).limit(limit).to_list()
            return organizations
        except Exception as e:
            logger.error(f"Failed to find organizations by name pattern {name_pattern}: {e}")
            raise DatabaseError(f"Failed to find organizations: {e}")
    
    async def slug_exists(self, slug: str) -> bool:
        """Check if slug already exists."""
        try:
            organization = await Organization.find_one(Organization.slug == slug)
            return organization is not None
        except Exception as e:
            logger.error(f"Failed to check slug existence {slug}: {e}")
            raise DatabaseError(f"Failed to check slug existence: {e}")
    
    async def get_organization_stats(self, org_id: str) -> Dict[str, Any]:
        """Get organization statistics."""
        try:
            organization = await self.get_by_id(org_id)
            if not organization:
                raise DocumentNotFoundError(f"Organization not found: {org_id}")
            
            # Import here to avoid circular imports
            from ..models.organization_member import OrganizationMember
            from ..models.project import Project
            from ..models.execution import Execution
            
            # Count members
            member_count = await OrganizationMember.find(
                OrganizationMember.org_id == org_id,
                OrganizationMember.is_active == True
            ).count()
            
            # Count projects
            project_count = await Project.find(
                Project.organization_id == org_id
            ).count()
            
            # Count executions
            execution_count = await Execution.find(
                Execution.organization_id == org_id
            ).count()
            
            stats = {
                "organization_id": org_id,
                "name": organization.name,
                "slug": organization.slug,
                "plan_type": organization.plan_type,
                "is_active": organization.is_active,
                "created_at": organization.created_at,
                "member_count": member_count,
                "project_count": project_count,
                "execution_count": execution_count,
            }
            
            return stats
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to get organization stats for {org_id}: {e}")
            raise DatabaseError(f"Failed to get organization stats: {e}")
    
    async def deactivate_organization(self, org_id: str) -> bool:
        """Deactivate an organization."""
        try:
            organization = await self.get_by_id(org_id)
            if not organization:
                raise DocumentNotFoundError(f"Organization not found: {org_id}")
            
            organization.is_active = False
            organization.update_timestamp()
            await organization.save()
            
            logger.info(f"Deactivated organization: {org_id}")
            return True
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to deactivate organization {org_id}: {e}")
            raise DatabaseError(f"Failed to deactivate organization: {e}")
    
    async def activate_organization(self, org_id: str) -> bool:
        """Activate an organization."""
        try:
            organization = await self.get_by_id(org_id)
            if not organization:
                raise DocumentNotFoundError(f"Organization not found: {org_id}")
            
            organization.is_active = True
            organization.update_timestamp()
            await organization.save()
            
            logger.info(f"Activated organization: {org_id}")
            return True
        except DocumentNotFoundError:
            raise
        except Exception as e:
            logger.error(f"Failed to activate organization {org_id}: {e}")
            raise DatabaseError(f"Failed to activate organization: {e}")
    
    async def count_total_organizations(self) -> int:
        """Count total number of organizations."""
        try:
            count = await Organization.count()
            return count
        except Exception as e:
            logger.error(f"Failed to count organizations: {e}")
            raise DatabaseError(f"Failed to count organizations: {e}")
    
    async def count_active_organizations(self) -> int:
        """Count active organizations."""
        try:
            count = await Organization.find(Organization.is_active == True).count()
            return count
        except Exception as e:
            logger.error(f"Failed to count active organizations: {e}")
            raise DatabaseError(f"Failed to count active organizations: {e}")
