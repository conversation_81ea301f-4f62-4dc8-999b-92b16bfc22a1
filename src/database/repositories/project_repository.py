"""
Project Repository for QAK MongoDB Integration

Repository implementation for Project entities with specialized queries.
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime, timedelta
from pymongo import ASCENDING, DESCENDING

from .base import BaseRepository, PaginationResult
from ..models.project import Project, TestSuite, TestCase, TestStatus
from ..exceptions import DocumentNotFoundError, DocumentExistsError

logger = logging.getLogger(__name__)


class ProjectRepository(BaseRepository[Project]):
    """Repository for Project entities with specialized operations."""
    
    def __init__(self):
        super().__init__("projects")
    
    def to_document(self, model: Project) -> Dict[str, Any]:
        """Convert Project model to MongoDB document."""
        return model.model_dump()
    
    def from_document(self, document: Dict[str, Any]) -> Project:
        """Convert MongoDB document to Project model."""
        return Project(**document)
    
    def get_document_id(self, model: Project) -> str:
        """Get the document ID from Project model."""
        return str(model.id) if model.id else ""
    
    # Specialized Project Queries
    
    async def get_by_project_id(self, project_id: str) -> Optional[Project]:
        """Get project by project_id field."""
        collection = await self.collection
        document = await collection.find_one({"project_id": project_id})
        if document:
            return self.from_document(document)
        else:
            return None
    
    async def exists_by_project_id(self, project_id: str) -> bool:
        """Check if project exists by project_id."""
        return await self.exists({"project_id": project_id})
    
    async def create_project(self, project: Project) -> Project:
        """Create a new project with duplicate checking."""
        # Check if project_id already exists
        if await self.exists_by_project_id(project.project_id):
            raise DocumentExistsError("projects", project.project_id)
        
        return await self.create(project)
    
    async def update_project(self, project: Project) -> Project:
        """Update an existing project."""
        existing = await self.get_by_project_id(project.project_id)
        if not existing:
            raise DocumentNotFoundError("projects", project.project_id)
        
        # Update the document ID for proper update
        project.id = existing.id
        return await self.update(project)
    
    async def delete_by_project_id(self, project_id: str) -> bool:
        """Delete project by project_id."""
        project = await self.get_by_project_id(project_id)
        if not project:
            return False
        
        return await self.delete_by_id(str(project.id))
    
    # Search and Filtering
    
    async def find_by_name(self, name: str, exact: bool = False) -> List[Project]:
        """Find projects by name (exact or partial match)."""
        if exact:
            query = {"name": name}
        else:
            query = {"name": {"$regex": name, "$options": "i"}}
        
        return await self.find(query, sort=[("name", ASCENDING)])
    
    async def find_by_tags(self, tags: List[str], match_all: bool = False) -> List[Project]:
        """Find projects by tags."""
        if match_all:
            # All tags must be present
            query = {"tags": {"$all": tags}}
        else:
            # Any tag matches
            query = {"tags": {"$in": tags}}
        
        return await self.find(query, sort=[("updated_at", DESCENDING)])
    
    async def find_by_description(self, description_text: str) -> List[Project]:
        """Find projects by description text (case-insensitive)."""
        query = {"description": {"$regex": description_text, "$options": "i"}}
        return await self.find(query, sort=[("name", ASCENDING)])
    
    async def search_projects(
        self,
        search_term: Optional[str] = None,
        tags: Optional[List[str]] = None,
        created_after: Optional[datetime] = None,
        created_before: Optional[datetime] = None,
        page: int = 1,
        page_size: int = 20
    ) -> PaginationResult[Project]:
        """Advanced project search with multiple filters."""
        query = {}
        
        # Text search across name and description
        if search_term:
            query["$or"] = [
                {"name": {"$regex": search_term, "$options": "i"}},
                {"description": {"$regex": search_term, "$options": "i"}}
            ]
        
        # Tag filtering
        if tags:
            query["tags"] = {"$in": tags}
        
        # Date range filtering
        date_filter = {}
        if created_after:
            date_filter["$gte"] = created_after
        if created_before:
            date_filter["$lte"] = created_before
        if date_filter:
            query["created_at"] = date_filter
        
        return await self.find_paginated(
            query=query,
            page=page,
            page_size=page_size,
            sort=[("updated_at", DESCENDING)]
        )
    
    # Test Suite Operations
    
    async def add_test_suite_to_project(self, project_id: str, test_suite: TestSuite) -> Project:
        """Add a test suite to a project."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        project.add_test_suite(test_suite)
        return await self.update_project(project)
    
    async def remove_test_suite_from_project(self, project_id: str, suite_id: str) -> Project:
        """Remove a test suite from a project."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        if not project.remove_test_suite(suite_id):
            logger.warning(f"Test suite {suite_id} not found in project {project_id}")
        
        return await self.update_project(project)
    
    async def get_project_test_suites(self, project_id: str) -> List[TestSuite]:
        """Get all test suites for a project."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        return list(project.test_suites.values())
    
    # Test Case Operations
    
    async def add_test_case_to_suite(
        self, 
        project_id: str, 
        suite_id: str, 
        test_case: TestCase
    ) -> Project:
        """Add a test case to a specific test suite."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        test_suite = project.get_test_suite(suite_id)
        if not test_suite:
            raise DocumentNotFoundError("test_suites", suite_id)
        
        test_suite.add_test_case(test_case)
        return await self.update_project(project)
    
    async def remove_test_case_from_suite(
        self, 
        project_id: str, 
        suite_id: str, 
        test_id: str
    ) -> Project:
        """Remove a test case from a specific test suite."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        test_suite = project.get_test_suite(suite_id)
        if not test_suite:
            raise DocumentNotFoundError("test_suites", suite_id)
        
        if not test_suite.remove_test_case(test_id):
            logger.warning(f"Test case {test_id} not found in suite {suite_id}")
        
        return await self.update_project(project)
    
    async def update_test_case_status(
        self, 
        project_id: str, 
        test_id: str, 
        status: TestStatus,
        execution_time: Optional[datetime] = None
    ) -> Project:
        """Update the status of a test case."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        test_case = project.get_test_case_by_id(test_id)
        if not test_case:
            raise DocumentNotFoundError("test_cases", test_id)
        
        test_case.status = status
        test_case.last_execution = execution_time or datetime.utcnow()
        test_case.update_timestamp()
        
        return await self.update_project(project)
    
    # Analytics and Statistics
    
    async def get_project_statistics(self, project_id: str) -> Dict[str, Any]:
        """Get comprehensive statistics for a project."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        return project.get_project_stats()
    
    async def get_projects_summary(self) -> Dict[str, Any]:
        """Get summary statistics for all projects."""
        try:
            collection = await self.collection
            
            # Aggregation pipeline for project statistics
            pipeline = [
                {
                    "$project": {
                        "project_id": 1,
                        "name": 1,
                        "suite_count": {"$size": {"$objectToArray": "$test_suites"}},
                        "test_case_count": {
                            "$sum": {
                                "$map": {
                                    "input": {"$objectToArray": "$test_suites"},
                                    "as": "suite",
                                    "in": {"$size": {"$objectToArray": "$$suite.v.test_cases"}}
                                }
                            }
                        },
                        "created_at": 1,
                        "updated_at": 1
                    }
                },
                {
                    "$group": {
                        "_id": None,
                        "total_projects": {"$sum": 1},
                        "total_suites": {"$sum": "$suite_count"},
                        "total_test_cases": {"$sum": "$test_case_count"},
                        "projects": {"$push": "$$ROOT"}
                    }
                }
            ]
            
            result = await collection.aggregate(pipeline).to_list(length=1)
            
            if result:
                return result[0]
            else:
                return {
                    "total_projects": 0,
                    "total_suites": 0,
                    "total_test_cases": 0,
                    "projects": []
                }
                
        except Exception as e:
            logger.error(f"Failed to get projects summary: {e}")
            return {
                "total_projects": 0,
                "total_suites": 0,
                "total_test_cases": 0,
                "projects": [],
                "error": str(e)
            }
    
    async def get_test_execution_trends(
        self, 
        project_id: Optional[str] = None,
        days: int = 30
    ) -> Dict[str, Any]:
        """Get test execution trends over time."""
        try:
            collection = await self.collection
            
            # Date range for trends
            end_date = datetime.utcnow()
            start_date = end_date - timedelta(days=days)
            
            # Base match criteria
            match_criteria = {}
            if project_id:
                match_criteria["project_id"] = project_id
            
            # Aggregation pipeline for execution trends
            pipeline = [
                {"$match": match_criteria},
                {
                    "$project": {
                        "project_id": 1,
                        "name": 1,
                        "test_cases": {
                            "$reduce": {
                                "input": {"$objectToArray": "$test_suites"},
                                "initialValue": [],
                                "in": {
                                    "$concatArrays": [
                                        "$$value",
                                        {"$objectToArray": "$$this.v.test_cases"}
                                    ]
                                }
                            }
                        }
                    }
                },
                {"$unwind": "$test_cases"},
                {
                    "$project": {
                        "project_id": 1,
                        "name": 1,
                        "test_case": "$test_cases.v",
                        "last_execution": "$test_cases.v.last_execution",
                        "status": "$test_cases.v.status"
                    }
                },
                {
                    "$match": {
                        "last_execution": {
                            "$gte": start_date,
                            "$lte": end_date
                        }
                    }
                },
                {
                    "$group": {
                        "_id": {
                            "date": {"$dateToString": {"format": "%Y-%m-%d", "date": "$last_execution"}},
                            "status": "$status"
                        },
                        "count": {"$sum": 1}
                    }
                },
                {
                    "$group": {
                        "_id": "$_id.date",
                        "executions": {
                            "$push": {
                                "status": "$_id.status",
                                "count": "$count"
                            }
                        },
                        "total": {"$sum": "$count"}
                    }
                },
                {"$sort": {"_id": 1}}
            ]
            
            results = await collection.aggregate(pipeline).to_list(length=None)
            
            return {
                "project_id": project_id,
                "date_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                    "days": days
                },
                "trends": results
            }
            
        except Exception as e:
            logger.error(f"Failed to get execution trends: {e}")
            return {
                "project_id": project_id,
                "error": str(e),
                "trends": []
            }
    
    # Bulk Operations
    
    async def bulk_update_test_statuses(
        self, 
        project_id: str, 
        status_updates: List[Tuple[str, TestStatus]]
    ) -> Project:
        """Bulk update test case statuses."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        updated_count = 0
        for test_id, status in status_updates:
            test_case = project.get_test_case_by_id(test_id)
            if test_case:
                test_case.status = status
                test_case.last_execution = datetime.utcnow()
                test_case.update_timestamp()
                updated_count += 1
            else:
                logger.warning(f"Test case {test_id} not found in project {project_id}")
        
        if updated_count > 0:
            project = await self.update_project(project)
            logger.info(f"Bulk updated {updated_count} test case statuses in project {project_id}")
        
        return project
    
    # Legacy Compatibility
    
    async def create_from_legacy_json(self, json_data: Dict[str, Any]) -> Project:
        """Create project from legacy JSON format."""
        project = Project.from_legacy_json(json_data)
        return await self.create_project(project)
    
    async def export_to_legacy_json(self, project_id: str) -> Dict[str, Any]:
        """Export project to legacy JSON format."""
        project = await self.get_by_project_id(project_id)
        if not project:
            raise DocumentNotFoundError("projects", project_id)
        
        return project.to_legacy_json()

    def _to_dict(self, model):
        """Helper to convert model to dict, handling nested Pydantic models."""
        return model.model_dump()

    async def get_by_project_id(self, project_id: str) -> Optional[Project]:
        collection = await self.collection
        document = await collection.find_one({"project_id": project_id})
        if document:
            return self.from_document(document)
        else:
            return None 