import os
import json
import shutil
import base64
from datetime import datetime
from typing import List, Dict, Any, Optional, Union, Tuple, TypeVar, Coroutine
from .project_manager import Project, TestSuite, TestCase, GitHubConfig
import asyncio
import logging

# New database service integration - Optional import with fallback
try:
    from ..services import ProjectService, ServiceResult
    DATABASE_SERVICES_AVAILABLE = True
except ImportError:
    DATABASE_SERVICES_AVAILABLE = False
    ProjectService = None
    ServiceResult = None

T = TypeVar('T')

def run_async(coro: Coroutine[Any, Any, T]) -> T:
    """
    Run an async function in a sync context.
    
    This function is designed to correctly run asynchronous code from a synchronous
    context, especially within a FastAPI/AnyIO application. It uses `anyio` to run
    the coroutine on the main event loop when called from a worker thread, which
    is the standard behavior in FastAPI for synchronous route handlers.
    
    If `anyio` is not available or the code is not running in an `anyio` thread
    (e.g., in a standalone script), it falls back to using `asyncio.run()`.
    """
    try:
        import anyio
        # This is the correct way to call an async function from a sync function
        # in a worker thread within an AnyIO-based application like FastAPI.
        # It runs the coroutine on the main AnyIO event loop.
        return anyio.from_thread.run(coro)
    except (ImportError, RuntimeError):
        # Fallback for environments where anyio is not used or not available,
        # such as standalone scripts or tests.
        return asyncio.run(coro)

def extract_screenshots_from_json(json_file_path):
    """
    Extrae las capturas de pantalla en base64 de un archivo JSON de historial y las devuelve como data URLs.
    Implementa manejo robusto de objetos problemáticos como DOMHistoryElement.

    Args:
        json_file_path: Ruta al archivo JSON de historial

    Returns:
        List[str]: Lista de data URLs (data:image/png;base64,...)
    """
    import json
    
    try:
        with open(json_file_path, "r") as f:
            history_data = json.load(f)

        # Extraer capturas de pantalla
        screenshot_data_urls = []

        # Buscar capturas en la estructura del historial usando método seguro
        try:
            for step in history_data.get("history", []):
                # Buscar en el estado con manejo de errores mejorado
                if "state" in step and isinstance(step["state"], dict) and "screenshot" in step["state"]:
                    screenshot = step["state"]["screenshot"]
                    if isinstance(screenshot, str) and len(screenshot) > 100:  # Probablemente es base64
                        try:
                            # Verificar si ya es una data URL
                            if screenshot.startswith("data:image"):
                                screenshot_data_urls.append(screenshot)
                            else:
                                # Es base64 puro, convertir a data URL
                                # Asumir que es PNG (la mayoría de las capturas de pantalla)
                                data_url = f"data:image/png;base64,{screenshot}"
                                screenshot_data_urls.append(data_url)
                        except Exception as e:
                            logging.info(f"⚠️ Error al procesar captura de pantalla: {str(e)}")
        except Exception as e:
            logging.info(f"⚠️ Error al procesar historial de pasos: {str(e)}")

        # Si no se encontraron capturas en el estado, buscar en model_actions
        if len(screenshot_data_urls) == 0:
            try:
                for action in history_data.get("model_actions", []):
                    if isinstance(action, dict) and "screenshot" in action:
                        screenshot = action["screenshot"]
                        if isinstance(screenshot, str) and len(screenshot) > 100:
                            try:
                                if screenshot.startswith("data:image"):
                                    screenshot_data_urls.append(screenshot)
                                else:
                                    data_url = f"data:image/png;base64,{screenshot}"
                                    screenshot_data_urls.append(data_url)
                            except Exception as e:
                                logging.info(f"⚠️ Error al procesar captura de model_actions: {str(e)}")
            except Exception as e:
                logging.info(f"⚠️ Error al procesar model_actions: {str(e)}")

        # Buscar en cualquier parte del JSON donde pueda haber una captura de pantalla usando método recursivo seguro
        if len(screenshot_data_urls) == 0:
            def extract_from_dict_safe(data, paths=None, prefix="", max_depth=10):
                """
                Extrae capturas de pantalla de manera recursiva con protección contra objetos problemáticos.
                """
                if paths is None:
                    paths = []
                    
                if max_depth <= 0:  # Prevenir recursión infinita
                    return paths

                try:
                    if isinstance(data, dict):
                        for key, value in data.items():
                            try:
                                if key == "screenshot" and isinstance(value, str) and len(value) > 100:
                                    paths.append((prefix + "." + key, value))
                                elif isinstance(value, (dict, list)) and max_depth > 0:
                                    extract_from_dict_safe(value, paths, prefix + "." + key if prefix else key, max_depth - 1)
                            except Exception as e:
                                # Skip problematic keys/values silently con más detalle en debug
                                if "DOMHistoryElement" in str(e):
                                    logging.info(f"🔍 Skipping DOMHistoryElement object at {prefix}.{key}")
                                continue
                    elif isinstance(data, list):
                        for i, item in enumerate(data):
                            try:
                                # Solo procesar tipos serializables básicos y limitar profundidad
                                if isinstance(item, (dict, list)) and max_depth > 0:
                                    extract_from_dict_safe(item, paths, f"{prefix}[{i}]", max_depth - 1)
                                elif isinstance(item, str) and len(item) > 100 and prefix.endswith("screenshot"):
                                    # Si es un string largo en un contexto de screenshot, podría ser base64
                                    paths.append((f"{prefix}[{i}]", item))
                            except Exception as e:
                                # Skip problematic items silently con más detalle en debug
                                if "DOMHistoryElement" in str(e):
                                    logging.info(f"🔍 Skipping DOMHistoryElement object at {prefix}[{i}]")
                                continue
                except Exception as e:
                    logging.info(f"⚠️ Error al procesar estructura de datos en {prefix}: {str(e)}")

                return paths

            # Extraer todas las capturas de pantalla del JSON con protección mejorada
            try:
                screenshot_entries = extract_from_dict_safe(history_data)

                # Procesar cada captura encontrada
                for i, (path, screenshot) in enumerate(screenshot_entries):
                    try:
                        if isinstance(screenshot, str) and len(screenshot) > 100:
                            if screenshot.startswith("data:image"):
                                screenshot_data_urls.append(screenshot)
                            else:
                                # Es base64 puro, convertir a data URL
                                data_url = f"data:image/png;base64,{screenshot}"
                                screenshot_data_urls.append(data_url)
                    except Exception as e:
                        logging.info(f"⚠️ Error al procesar captura de pantalla {i+1}: {str(e)}")
            except Exception as e:
                logging.info(f"⚠️ Error al extraer capturas del JSON: {str(e)}")

        logging.info(f"📸 Extraídas {len(screenshot_data_urls)} capturas de pantalla desde {json_file_path}")
        return screenshot_data_urls
        
    except json.JSONDecodeError as e:
        logging.info(f"❌ Error al decodificar JSON: {str(e)}")
        return []
    except Exception as e:
        logging.info(f"❌ Error al extraer capturas de pantalla: {str(e)}")
        return []

def _analyze_step_success(step: dict, llm=None) -> bool:
    """Analiza si un paso fue exitoso usando IA.
    
    Args:
        step: Diccionario con información del paso
        llm: Instancia del modelo de lenguaje (opcional)
        
    Returns:
        bool: True si el paso fue exitoso, False si falló
    """
    from datetime import datetime

    try:
        # 0) Si ya existe un resultado cacheado, usarlo y evitar trabajo extra
        cached_success = (
            step.get("analysis", {}).get("success")
            if isinstance(step.get("analysis"), dict) else None
        )
        if cached_success is not None:
            return bool(cached_success)

        # 1) Heurística básica (rápida)  ------------------------------------
        all_text = str(step).lower()
        negative_keywords = [
            "error", "fail", "failed", "exception", "timeout", "unsuccessful",
            "not found", "unable", "denied", "invalid", "crash"
        ]
        positive_keywords = [
            "success", "successful", "completed", "done", "passed", "clicked",
            "navigated", "opened"
        ]

        neg_hits = sum(kw in all_text for kw in negative_keywords)
        pos_hits = sum(kw in all_text for kw in positive_keywords)

        if pos_hits - neg_hits >= 2:
            success = True
            source = "heuristic"
        elif neg_hits >= 1:
            success = False
            source = "heuristic"
        else:
            # 2) Llamada al LLM si está disponible -------------------------
            if llm_factory is not None:
                # Extraer contexto mínimo para el prompt
                action_txt = ""
                if step.get("model_output") and "action" in step["model_output"]:
                    actions = step["model_output"]["action"]
                    action_txt = str(actions[0] if isinstance(actions, list) else actions)

                description_txt = step.get("state", {}).get("description", "")

                result_parts = []
                for res in step.get("result", []):
                    if isinstance(res, dict):
                        result_parts.append(str(res.get("extracted_content", "")))
                result_txt = " ".join(result_parts)

                prompt = (
                    "You are a QA assistant. "
                    "Return ONLY one word: 'success' or 'failed'.\n"
                    f"Action: {action_txt}\n"
                    f"Description: {description_txt}\n"
                    f"Result: {result_txt}\n"
                    "Answer:"  # LLM should reply with single token ideally
                )

                try:
                    # Use new unified LLM architecture
                    request = LLMRequest(
                        messages=[
                            {
                                "role": "system",
                                "content": "You are a QA assistant. Return ONLY one word: 'success' or 'failed'."
                            },
                            {
                                "role": "user", 
                                "content": f"Action: {action_txt}\nDescription: {description_txt}\nResult: {result_txt}\nAnswer:"
                            }
                        ],
                        use_case="analysis",
                        language="en",
                        max_tokens=10,
                        temperature=0.0
                    )
                    
                    response = llm_factory.make_request(request)
                    if response.success:
                        response_text = response.content.lower().strip()
                        success = response_text.startswith("success")
                        source = "llm"
                    else:
                        # Fallback if LLM fails
                        success = neg_hits == 0
                        source = "heuristic"
                        
                except Exception as e:
                    logging.info(f"LLM analysis failed: {e}")
                    # Fallback: asumir éxito si no hay señales negativas
                    success = neg_hits == 0
                    source = "heuristic"
            else:
                # Sin LLM: asumir éxito si no vimos errores
                success = neg_hits == 0
                source = "heuristic"

        # 3) Cachear resultado en el paso ----------------------------------
        step["analysis"] = {
            "success": bool(success),
            "source": source,
            "timestamp": datetime.utcnow().isoformat()
        }

        return bool(success)

    except Exception as e:
        logging.info(f"Error en análisis del paso: {str(e)}")
        # Si algo falla, marcar como fallido para seguridad
        step["analysis"] = {
            "success": False,
            "source": "error",
            "timestamp": datetime.utcnow().isoformat()
        }
        return False

def load_test_history(json_file_path):
    """
    Carga y procesa un archivo JSON de historial de pruebas para su visualización.
    
    Args:
        json_file_path: Ruta al archivo JSON de historial
        
    Returns:
        dict: Datos procesados del historial de pruebas
    """
    import json, os
    try:
        # Permitir desactivar completamente el uso de LLM para el análisis de historiales
        use_llm = os.getenv("HISTORY_ANALYSIS_USE_LLM", "false").lower() in ("1", "true", "yes")

        if use_llm:
            # Use the new unified LLM architecture
            from src.services.llm.llm_service_factory import get_llm_factory
            from src.services.llm.base_llm_service import LLMRequest
            llm_factory = get_llm_factory()
        else:
            llm_factory = None

        with open(json_file_path, "r") as f:
            history_data = json.load(f)

        # Verificar si el historial está vacío
        if not history_data.get("history") or len(history_data.get("history", [])) == 0:
            return {
                "actions": [],
                "results": [],
                "elements": [],
                "urls": [],
                "errors": ["El historial de ejecución está vacío. La prueba no se completó correctamente."],
                "screenshots": [],
                "metadata": {
                    "start_time": None,
                    "end_time": None,
                    "total_steps": 0,
                    "success": False,
                    "empty_history": True,
                    "file_path": json_file_path
                }
            }

        # Procesar los datos para visualización
        processed_data = {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "empty_history": False,
                "file_path": json_file_path
            }
        }

        # Indicador para saber si agregamos nuevas anotaciones y necesitamos persistir cambios
        dirty = False

        # Extraer información de cada paso
        for step in history_data.get("history", []):
            # Extraer acciones
            if "model_output" in step and step["model_output"] is not None and "action" in step["model_output"]:
                for action in step["model_output"]["action"]:
                    try:
                        # Limpiar la acción para evitar problemas de serialización
                        if isinstance(action, dict):
                            # Crear una copia limpia de la acción
                            clean_action = {}
                            for key, value in action.items():
                                try:
                                    # Solo incluir valores serializables
                                    if isinstance(value, (str, int, float, bool, type(None))):
                                        clean_action[key] = value
                                    elif isinstance(value, (dict, list)):
                                        # Para diccionarios y listas, intentar serializar
                                        try:
                                            json.dumps(value)  # Test serialization
                                            clean_action[key] = value
                                        except (TypeError, ValueError):
                                            # Si no se puede serializar, convertir a string
                                            clean_action[key] = str(value)
                                    else:
                                        # Convertir objetos complejos a string
                                        clean_action[key] = str(value)
                                except Exception:
                                    # Si hay error, convertir a string
                                    clean_action[key] = str(value)
                            
                            processed_data["actions"].append({
                                "type": list(clean_action.keys())[0] if clean_action else "unknown",
                                "details": clean_action,
                                "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                            })
                        else:
                            # Si la acción no es un diccionario, convertir a string
                            processed_data["actions"].append({
                                "type": "unknown",
                                "details": {"raw_action": str(action)},
                                "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                            })
                    except Exception as e:
                        logging.info(f"Error al procesar acción en paso {step.get('metadata', {}).get('step_number', 0)}: {str(e)}")
                        # En caso de error, agregar una acción básica
                        processed_data["actions"].append({
                            "type": "error",
                            "details": {"error": f"Error al procesar acción: {str(e)}"},
                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                        })

            # Extraer resultados
            if "result" in step:
                for result in step["result"]:
                    try:
                        if "extracted_content" in result:
                            # Determinar si este paso ya contaba con análisis
                            prev_cached = step.get("analysis", {}).get("success") is not None
                            # Analizar o recuperar éxito del paso (si ya cacheado)
                            success = _analyze_step_success(step, llm)
                            if not prev_cached:
                                dirty = True

                            processed_data["results"].append({
                                "content": result["extracted_content"],
                                "is_done": result.get("is_done", False),
                                "success": success,
                                "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                            })
                    except Exception as e:
                        logging.info(f"Error al procesar resultado en paso {step.get('metadata', {}).get('step_number', 0)}: {str(e)}")
                        processed_data["results"].append({
                            "content": f"Error al procesar resultado: {str(e)}",
                            "is_done": False,
                            "success": False,
                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                        })

            # Extraer elementos interactuados
            if "state" in step and step["state"] is not None and "interacted_element" in step["state"]:
                try:
                    for element in step["state"]["interacted_element"]:
                        if element:
                            # Limpiar elemento para evitar problemas de serialización
                            try:
                                # Si el elemento es un objeto complejo, extraer solo datos serializables
                                if hasattr(element, '__dict__') or hasattr(element, '__class__'):
                                    # Es un objeto, convertir a diccionario básico con manejo seguro
                                    element_dict = {}
                                    try:
                                        if hasattr(element, 'tag_name'):
                                            element_dict["tag_name"] = str(element.tag_name) if element.tag_name else ""
                                        else:
                                            element_dict["tag_name"] = ""
                                    except:
                                        element_dict["tag_name"] = ""
                                    
                                    try:
                                        if hasattr(element, 'xpath'):
                                            element_dict["xpath"] = str(element.xpath) if element.xpath else ""
                                        else:
                                            element_dict["xpath"] = ""
                                    except:
                                        element_dict["xpath"] = ""
                                    
                                    try:
                                        if hasattr(element, 'attributes'):
                                            element_dict["attributes"] = dict(element.attributes) if element.attributes else {}
                                        else:
                                            element_dict["attributes"] = {}
                                    except:
                                        element_dict["attributes"] = {}
                                    
                                    processed_data["elements"].append({
                                        **element_dict,
                                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                    })
                                elif isinstance(element, dict):
                                    # Es un diccionario, procesar normalmente
                                    processed_data["elements"].append({
                                        "tag_name": element.get("tag_name", ""),
                                        "xpath": element.get("xpath", ""),
                                        "attributes": element.get("attributes", {}),
                                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                    })
                                else:
                                    # Tipo no reconocido, convertir a string de manera segura
                                    try:
                                        element_str = str(element)
                                        processed_data["elements"].append({
                                            "tag_name": "",
                                            "xpath": "",
                                            "attributes": {},
                                            "raw_data": element_str,
                                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                        })
                                    except:
                                        # Si hasta str() falla, usar un marcador de posición
                                        processed_data["elements"].append({
                                            "tag_name": "unknown",
                                            "xpath": "",
                                            "attributes": {},
                                            "raw_data": "Could not serialize element",
                                            "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                        })
                            except Exception as e:
                                logging.info(f"Error al procesar elemento en paso {step.get('metadata', {}).get('step_number', 0)}: {str(e)}")
                                # En caso de error, agregar un elemento básico
                                processed_data["elements"].append({
                                    "tag_name": "",
                                    "xpath": "",
                                    "attributes": {},
                                    "error": f"Error al procesar elemento: {str(e)}",
                                    "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                                })
                except Exception as e:
                    logging.info(f"Error al procesar lista de elementos en paso {step.get('metadata', {}).get('step_number', 0)}: {str(e)}")
                    # En caso de error, agregar un elemento básico
                    processed_data["elements"].append({
                        "tag_name": "",
                        "xpath": "",
                        "attributes": {},
                        "raw_data": f"Error: {str(e)}",
                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                    })

            # Extraer URLs
            if "state" in step and step["state"] is not None and "url" in step["state"]:
                url = step["state"]["url"]
                if url and url != "about:blank":
                    processed_data["urls"].append({
                        "url": url,
                        "title": step["state"].get("title", ""),
                        "step": step["metadata"].get("step_number", 0) if "metadata" in step else 0
                    })

            # Extraer metadatos
            if "metadata" in step:
                if processed_data["metadata"]["start_time"] is None or step["metadata"].get("step_start_time", 0) < processed_data["metadata"]["start_time"]:
                    processed_data["metadata"]["start_time"] = step["metadata"].get("step_start_time", 0)

                if processed_data["metadata"]["end_time"] is None or step["metadata"].get("step_end_time", 0) > processed_data["metadata"]["end_time"]:
                    processed_data["metadata"]["end_time"] = step["metadata"].get("step_end_time", 0)

                processed_data["metadata"]["total_steps"] = max(processed_data["metadata"]["total_steps"], step["metadata"].get("step_number", 0))

        # Verificar si la prueba fue exitosa
        last_results = [r for r in processed_data["results"] if r.get("is_done", False)]
        if last_results:
            processed_data["metadata"]["success"] = last_results[-1].get("success", False)

        # Extraer y procesar capturas de pantalla
        screenshot_data_urls = extract_screenshots_from_json(json_file_path)
        processed_data["screenshots"] = screenshot_data_urls

        # 4) Persistir las anotaciones de análisis si correspondiera ---------
        try:
            writeback_enabled = os.getenv("HISTORY_ANALYSIS_WRITEBACK", "true").lower() in ("1", "true", "yes")
            if writeback_enabled and dirty:
                import tempfile, json as _json, os as _os
                dir_name = _os.path.dirname(json_file_path)
                fd, tmp_path = tempfile.mkstemp(dir=dir_name, suffix=".tmp", text=True)
                with _os.fdopen(fd, "w", encoding="utf-8") as tmp_f:
                    _json.dump(history_data, tmp_f, indent=2, ensure_ascii=False)
                _os.replace(tmp_path, json_file_path)
        except Exception as e:
            logging.info(f"⚠️  No se pudo escribir el análisis en cache: {e}")

        return processed_data

    except FileNotFoundError:
        logging.info(f"Error: No se pudo encontrar el archivo {json_file_path}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Archivo de historial no encontrado: {json_file_path}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "file_not_found": True,
                "file_path": json_file_path
            }
        }
    except json.JSONDecodeError as e:
        logging.info(f"Error al decodificar JSON: {str(e)}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Error al leer el archivo de historial: {str(e)}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "json_error": True,
                "file_path": json_file_path
            }
        }
    except Exception as e:
        logging.info(f"Error inesperado al cargar historial: {str(e)}")
        return {
            "actions": [],
            "results": [],
            "elements": [],
            "urls": [],
            "errors": [f"Error inesperado al cargar historial: {str(e)}"],
            "screenshots": [],
            "metadata": {
                "start_time": None,
                "end_time": None,
                "total_steps": 0,
                "success": False,
                "unexpected_error": True,
                "file_path": json_file_path
            }
        }


class ProjectManagerService:
    """
    Servicio para gestionar proyectos, suites de pruebas y casos de prueba.
    
    Ahora con soporte opcional para base de datos MongoDB mientras mantiene
    compatibilidad total con el sistema de archivos JSON existente.
    """
    def __init__(self, base_dir: str = "projects", use_database: Optional[bool] = None):
        """
        Inicializa el gestor de proyectos.
        
        Args:
            base_dir: Directorio base (legacy parameter, ignored)
            use_database: Si usar base de datos (legacy parameter, ignored - always uses database)
        """
        # Import here to avoid circular imports
        from ..services.project_service import ProjectService
        
        # Always use MongoDB - no more dual mode
        self._project_service = ProjectService()
        self.projects: Dict[str, Project] = {}
        
        logging.info("✅ ProjectManagerService: MongoDB mode enabled")
        
        # Load initial projects - THIS CANNOT BE ASYNC IN __init__
        # self._load_initial_projects()
    
    @property
    def is_database_enabled(self) -> bool:
        """Always returns True as we only use database now."""
        return True
    
    async def _load_initial_projects(self):
        """Load projects from MongoDB on initialization."""
        try:
            result = await self._project_service.list_projects(page_size=1000)
            if result and result.success:
                projects = {}
                for db_project in result.data.get("projects", []):
                    legacy_proj = self._convert_db_to_legacy_project(db_project)
                    projects[legacy_proj.project_id] = legacy_proj
                self.projects = projects
            else:
                self.projects = {}

            logging.info(f"📦 Loaded {len(self.projects)} projects from MongoDB")
            
        except Exception as e:
            logging.info(f"⚠️ Error loading initial projects: {e}")
            self.projects = {}
    
    def _convert_db_to_legacy_project(self, db_project) -> Project:
        """Convert database project model to legacy project format."""
        try:
            # Use the legacy JSON conversion method from the database model
            legacy_data = db_project.to_legacy_json()
            return Project.from_dict(legacy_data)
        except Exception as e:
            logging.info(f"⚠️ Error converting database project to legacy format: {e}")
            # Fallback manual conversion
            return Project(
                project_id=db_project.project_id,
                name=db_project.name,
                description=db_project.description,
                tags=db_project.tags,
                github_config=db_project.github_config
            )
    
    def _convert_legacy_to_db_project(self, legacy_project: Project):
        """Convert legacy project to database model format."""
        try:
            # Import here to avoid circular imports
            from ..database.models import Project as DbProject
            return DbProject.from_legacy_json(legacy_project.to_dict())
        except Exception as e:
            logging.info(f"⚠️ Error converting legacy project to database format: {e}")
            return None

    def ensure_directories(self) -> None:
        """Legacy method - no longer needed."""
        pass

    async def load_projects(self) -> Dict[str, Project]:
        """Load all projects from MongoDB."""
        try:
            result = await self._project_service.list_projects(page_size=1000)
            if result and result.success:
                projects = {}
                for db_project in result.data.get("projects", []):
                    legacy_proj = self._convert_db_to_legacy_project(db_project)
                    projects[legacy_proj.project_id] = legacy_proj
                self.projects = projects
            else:
                self.projects = {}
            
            logging.debug(f"📦 Reloaded {len(self.projects)} projects from MongoDB")
            return self.projects
            
        except Exception as e:
            logging.info(f"⚠️ Error loading projects: {e}")
            return {}

    async def save_project(self, project: Project) -> bool:
        """Save project to MongoDB."""
        try:
            # Convert the entire Project object to a dict
            project_dict = project.to_dict()
            
            # Use the complete project update method to ensure all data is saved
            result = await self._project_service.update_complete_project(
                project_id=project.project_id,
                project_data=project_dict
            )
            success = result and result.success

            if success:
                # Update local cache
                self.projects[project.project_id] = project
                logging.info(f"✅ Successfully saved project with ID: {project.project_id}")
            else:
                logging.info(f"❌ Failed to save project with ID: {project.project_id}")
                if result and result.message:
                    logging.info(f"Error: {result.message}")
                    
            return success
            
        except Exception as e:
            logging.info(f"⚠️ Error saving project: {e}")
            return False

    async def create_project(self, name: str, description: str = "", tags: List[str] = None,
                      github_config: Optional[GitHubConfig] = None) -> Project:
        """Create a new project in MongoDB.
        
        Args:
            name: Project name
            description: Project description
            tags: List of tags
            github_config: GitHub configuration

        Returns:
            Project: Created project
        """
        try:
            # Convert github_config to dict if needed
            gh_config_dict = github_config.to_dict() if github_config else None
            
            result = await self._project_service.create_project(
                name=name,
                description=description,
                tags=tags or [],
                github_config=gh_config_dict
            )
            db_project = result.data if result and result.success else None
            
            if db_project:
                # Convert to legacy format and cache
                legacy_project = self._convert_db_to_legacy_project(db_project)
                self.projects[legacy_project.project_id] = legacy_project
                return legacy_project
            else:
                # Fallback - create locally (shouldn't happen)
                logging.info("⚠️ Failed to create project in database")
                raise Exception("Failed to create project in database")
                
        except Exception as e:
            logging.info(f"⚠️ Error creating project: {e}")
            raise

    async def get_project(self, project_id: str) -> Optional[Project]:
        """Obtiene un proyecto por su ID."""
        # This now reloads from DB every time to ensure freshness
        await self.load_projects()
        return self.projects.get(project_id)

    async def get_all_projects(self) -> List[Project]:
        """Obtiene todos los proyectos."""
        await self.load_projects()
        return list(self.projects.values())

    async def update_project(self, project_id: str, name: str = None, description: str = None, 
                      tags: List[str] = None, github_config: Optional[GitHubConfig] = None) -> Optional[Project]:
        """Actualiza un proyecto existente."""
        await self.load_projects() # Ensure cache is fresh
        project = self.projects.get(project_id)
        if not project:
            return None

        # Update local object
        if name is not None:
            project.name = name
        if description is not None:
            project.description = description
        if tags is not None:
            project.tags = tags
        if github_config is not None:
            project.github_config = github_config

        project.updated_at = datetime.now().isoformat()
        
        # Save to database, which will also update the cache
        if await self.save_project(project):
            return project
        return None

    async def delete_project(self, project_id: str) -> bool:
        """Elimina un proyecto."""
        if project_id not in self.projects:
            await self.load_projects() # Ensure cache is fresh just in case
        
        if project_id not in self.projects:
            return False

        try:
            result = await self._project_service.delete_project(project_id)
            success = result and result.success
            if success:
                # Remove from local cache
                del self.projects[project_id]
                return True
            return False
            
        except Exception as e:
            logging.info(f"⚠️ Error deleting project: {e}")
            return False

    async def create_test_suite(self, project_id: str, name: str, description: str = "", tags: List[str] = None, execution_times: int = 1) -> Optional[TestSuite]:
        """Crea una nueva suite de pruebas en un proyecto."""
        project = await self.get_project(project_id)
        if not project:
            return None

        test_suite = TestSuite(name=name, description=description, tags=tags, execution_times=execution_times)
        project.add_test_suite(test_suite)
        
        # Save to database
        if await self.save_project(project):
            return test_suite
        return None

    async def get_test_suite(self, project_id: str, suite_id: str) -> Optional[TestSuite]:
        """Obtiene una suite de pruebas por su ID."""
        project = await self.get_project(project_id)
        if not project:
            return None

        return project.get_test_suite(suite_id)

    async def update_test_suite(self, project_id: str, suite_id: str, name: str = None, description: str = None, tags: List[str] = None, execution_times: int = None) -> Optional[TestSuite]:
        """Actualiza una suite de pruebas existente."""
        await self.load_projects() # Ensure cache is fresh
        project = self.projects.get(project_id)
        if not project:
            return None

        test_suite = project.get_test_suite(suite_id)
        if not test_suite:
            return None

        if name is not None:
            test_suite.name = name
        if description is not None:
            test_suite.description = description
        if tags is not None:
            test_suite.tags = tags
        if execution_times is not None:
            test_suite.set_execution_times(execution_times)

        test_suite.updated_at = datetime.now().isoformat()
        
        # Save the parent project to database
        if await self.save_project(project):
            return test_suite
        return None

    async def delete_test_suite(self, project_id: str, suite_id: str) -> bool:
        """Elimina una suite de pruebas."""
        await self.load_projects()
        project = self.projects.get(project_id)
        if not project:
            return False

        if project.remove_test_suite(suite_id):
            return await self.save_project(project)
        return False

    async def create_test_case(self, project_id: str, suite_id: str, name: str, description: str = "",
                         instrucciones: str = "", historia_de_usuario: str = "",
                         gherkin: str = "", url: str = "", tags: List[str] = None) -> Optional[TestCase]:
        """Crea un nuevo caso de prueba en una suite."""
        project = await self.get_project(project_id)
        if not project:
            return None

        test_suite = await self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return None

        test_case = TestCase(name=name, description=description, instrucciones=instrucciones,
                             historia_de_usuario=historia_de_usuario, gherkin=gherkin,
                             url=url, tags=tags)
        test_suite.add_test_case(test_case)

        # Save to database
        if await self.save_project(project):
            return test_case
        return None

    async def get_test_case(self, project_id: str, suite_id: str, test_id: str) -> Optional[TestCase]:
        """Obtiene un caso de prueba por su ID."""
        project = await self.get_project(project_id)
        if not project:
            return None

        test_suite = await self.get_test_suite(project_id, suite_id)
        if not test_suite:
            return None

        return test_suite.get_test_case(test_id)

    async def update_test_case(self, project_id: str, suite_id: str, test_id: str,
                         name: str = None, description: str = None,
                         instrucciones: str = None, historia_de_usuario: str = None,
                         gherkin: str = None, url: str = None, tags: List[str] = None) -> Optional[TestCase]:
        """Actualiza un caso de prueba existente."""
        await self.load_projects() # Ensure cache is fresh
        project = self.projects.get(project_id)
        if not project:
            return None

        test_suite = project.get_test_suite(suite_id)
        if not test_suite:
            return None

        test_case = test_suite.get_test_case(test_id)
        if not test_case:
            return None

        if name is not None:
            test_case.name = name
        if description is not None:
            test_case.description = description
        if instrucciones is not None:
            test_case.instrucciones = instrucciones
        if historia_de_usuario is not None:
            test_case.historia_de_usuario = historia_de_usuario
        if gherkin is not None:
            test_case.gherkin = gherkin
        if url is not None:
            test_case.url = url
        if tags is not None:
            test_case.tags = tags

        test_case.updated_at = datetime.now().isoformat()
        
        # Save to database
        if await self.save_project(project):
            return test_case
        return None

    async def update_test_case_status(self, project_id: str, suite_id: str, test_id: str, status: str) -> Optional[TestCase]:
        """Actualiza el estado de un caso de prueba."""
        project = await self.get_project(project_id)
        if not project:
            return None

        test_case = await self.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return None

        test_case.status = status
        test_case.last_execution = datetime.now().isoformat()
        test_case.updated_at = datetime.now().isoformat()
        
        # Save to database
        if await self.save_project(project):
            return test_case
        return None

    async def add_history_to_test_case(self, project_id: str, suite_id: str, test_id: str, history_path: str) -> Optional[TestCase]:
        """Añade un archivo de historial a un caso de prueba."""
        project = await self.get_project(project_id)
        if not project:
            return None

        test_case = await self.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return None

        test_case.add_history_file(history_path)
        
        # Save to database
        if await self.save_project(project):
            return test_case
        return None

    async def delete_test_case(self, project_id: str, suite_id: str, test_id: str) -> bool:
        """Elimina un caso de prueba."""
        await self.load_projects()
        project = self.projects.get(project_id)
        if not project:
            return False

        test_suite = project.get_test_suite(suite_id)
        if not test_suite:
            return False

        if test_suite.remove_test_case(test_id):
            return await self.save_project(project)
        return False
    
    # Advanced Database Features (when database mode is enabled)
    
    async def search_projects(self, search_term: str, tags: List[str] = None, page: int = 1, page_size: int = 20) -> Dict[str, Any]:
        """Search projects in MongoDB with pagination."""
        try:
            result = await self._project_service.list_projects(
                search_term=search_term,
                tags=tags,
                page=page,
                page_size=page_size
            )
            if result and result.success:
                legacy_projects = [self._convert_db_to_legacy_project(p) for p in result.data.get("projects", [])]
                return {
                    "projects": [p.to_dict() for p in legacy_projects],
                    "total": result.data.get("total", 0),
                    "page": result.data.get("page", 1),
                    "pages": result.data.get("pages", 1)
                }
            return {"projects": [], "total": 0, "page": 1, "pages": 1}
        except Exception as e:
            logging.info(f"⚠️ Error searching projects: {e}")
            return {"projects": [], "total": 0, "page": 1, "pages": 1}
    
    async def get_project_analytics(self) -> Dict[str, Any]:
        """
        Get analytics for all projects from the service layer.
        """
        try:
            result = await self._project_service.get_project_analytics()
            if result and result.success:
                return result.data
            
            logging.info(f"⚠️ Failed to get project analytics: {result.error if result else 'Unknown error'}")
            return {
                "total_projects": 0,
                "total_suites": 0,
                "total_test_cases": 0,
                "status_breakdown": {},
                "most_active_projects": [],
                "recent_failures": []
            }
        except Exception as e:
            logging.info(f"⚠️ Error getting project analytics: {e}")
            return {}

    async def migrate_to_database(self, dry_run: bool = True, batch_size: int = 10) -> Dict[str, Any]:
        """
        Migrate all legacy file-based projects to the database.
        """
        try:
            result = await self._project_service.migrate_from_legacy(
                dry_run=dry_run,
                batch_size=batch_size
            )
            if result and result.success:
                return result.data
            return {"status": "failed", "error": result.error if result else "Unknown"}
        except Exception as e:
            logging.info(f"⚠️ Error migrating projects: {e}")
            return {"status": "failed", "error": str(e)}

    async def get_service_health(self) -> Dict[str, Any]:
        """
        Get the health status of the project service and its database connection.
        """
        health_info = {
            "service_name": "ProjectManagerService",
            "status": "healthy",
            "database_health": {}
        }
        try:
            result = await self._project_service.health_check()
            if result and result.success:
                health_info["database_health"] = result.data
            else:
                health_info["status"] = "unhealthy"
                health_info["database_health"] = {"status": "error", "error": result.error if result else "Unknown"}
        except Exception as e:
            health_info["status"] = "unhealthy"
            health_info["error"] = str(e)
            
        return health_info


