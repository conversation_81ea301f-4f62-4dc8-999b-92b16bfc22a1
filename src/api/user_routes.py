"""
User Management API Routes for QAK Multi-Tenant System

Admin endpoints for user management and system administration.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, HTTPException, status, Depends, Query
from pydantic import BaseModel, EmailStr, Field

from src.services.user_service import UserService
from src.services.organization_service import OrganizationService
from src.core.auth_middleware import get_current_user_id
from src.core.role_middleware import require_system_admin
from src.database.models.user import User
from src.database.exceptions import DatabaseError, DocumentNotFoundError

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/admin/users", tags=["User Management"])

# Initialize services
user_service = UserService()
org_service = OrganizationService()


# Request/Response Models

class UserSearchResponse(BaseModel):
    """User search response."""
    users: List[dict]
    total: int
    page: int
    limit: int


class UserStatsResponse(BaseModel):
    """User statistics response."""
    user_id: str
    email: str
    full_name: str
    is_active: bool
    email_verified: bool
    created_at: str
    organization_count: int
    project_count: int
    execution_count: int


class UpdateUserRequest(BaseModel):
    """Admin update user request."""
    full_name: Optional[str] = Field(None, min_length=1, description="User's full name")
    email: Optional[EmailStr] = Field(None, description="User email address")
    is_active: Optional[bool] = Field(None, description="User active status")
    email_verified: Optional[bool] = Field(None, description="Email verification status")


# User Management Endpoints (System Admin Only)

@router.get("/search", response_model=UserSearchResponse)
async def search_users(
    q: str = Query(..., min_length=1, description="Search query"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(50, ge=1, le=100, description="Items per page"),
    active_only: bool = Query(True, description="Search only active users"),
    _: bool = Depends(require_system_admin())
):
    """Search users. Requires system admin access."""
    try:
        skip = (page - 1) * limit
        users = await user_service.search_users(
            query=q,
            limit=limit,
            skip=skip,
            active_only=active_only
        )
        
        # Get total count (simplified - in production you'd want a separate count query)
        total = len(users) + skip  # Approximation
        
        return UserSearchResponse(
            users=[user.dict() for user in users],
            total=total,
            page=page,
            limit=limit
        )
        
    except DatabaseError as e:
        logger.error(f"Failed to search users: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to search users"
        )


@router.get("/{user_id}", response_model=dict)
async def get_user_details(
    user_id: str,
    _: bool = Depends(require_system_admin())
):
    """Get detailed user information. Requires system admin access."""
    try:
        user = await user_service.get_user_by_id(user_id)
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Get user profile with organizations
        profile = await user_service.get_user_profile(user_id)
        
        return {
            "user": user.dict(),
            "profile": profile
        }
        
    except HTTPException:
        raise
    except DatabaseError as e:
        logger.error(f"Failed to get user details: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user details"
        )


@router.get("/{user_id}/stats", response_model=UserStatsResponse)
async def get_user_stats(
    user_id: str,
    _: bool = Depends(require_system_admin())
):
    """Get user statistics. Requires system admin access."""
    try:
        stats = await user_service.get_user_stats(user_id)
        
        return UserStatsResponse(
            user_id=stats["user_id"],
            email=stats["email"],
            full_name=stats["full_name"],
            is_active=stats["is_active"],
            email_verified=stats["email_verified"],
            created_at=stats["created_at"].isoformat(),
            organization_count=stats["organization_count"],
            project_count=stats["project_count"],
            execution_count=stats["execution_count"]
        )
        
    except DocumentNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    except DatabaseError as e:
        logger.error(f"Failed to get user stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user stats"
        )


@router.put("/{user_id}")
async def update_user(
    user_id: str,
    request: UpdateUserRequest,
    admin_user_id: str = Depends(get_current_user_id),
    _: bool = Depends(require_system_admin())
):
    """Update user information. Requires system admin access."""
    try:
        # Filter out None values
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No data provided for update"
            )
        
        # Prevent admin from deactivating themselves
        if user_id == admin_user_id and update_data.get("is_active") is False:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate your own account"
            )
        
        updated_user = await user_service.update_user_profile(user_id, update_data)
        
        logger.info(f"User updated by admin {admin_user_id}: {user_id}")
        
        return {
            "message": "User updated successfully",
            "user": updated_user.dict()
        }
        
    except HTTPException:
        raise
    except DocumentNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Failed to update user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update user"
        )


@router.post("/{user_id}/activate")
async def activate_user(
    user_id: str,
    admin_user_id: str = Depends(get_current_user_id),
    _: bool = Depends(require_system_admin())
):
    """Activate a user account. Requires system admin access."""
    try:
        success = await user_service.activate_user(user_id)
        
        if success:
            logger.info(f"User activated by admin {admin_user_id}: {user_id}")
            return {"message": "User activated successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to activate user"
            )
            
    except DocumentNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    except DatabaseError as e:
        logger.error(f"Failed to activate user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to activate user"
        )


@router.post("/{user_id}/deactivate")
async def deactivate_user(
    user_id: str,
    admin_user_id: str = Depends(get_current_user_id),
    _: bool = Depends(require_system_admin())
):
    """Deactivate a user account. Requires system admin access."""
    try:
        # Prevent admin from deactivating themselves
        if user_id == admin_user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot deactivate your own account"
            )
        
        success = await user_service.deactivate_user(user_id)
        
        if success:
            logger.info(f"User deactivated by admin {admin_user_id}: {user_id}")
            return {"message": "User deactivated successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to deactivate user"
            )
            
    except HTTPException:
        raise
    except DocumentNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    except DatabaseError as e:
        logger.error(f"Failed to deactivate user: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to deactivate user"
        )


@router.get("/{user_id}/organizations")
async def get_user_organizations(
    user_id: str,
    _: bool = Depends(require_system_admin())
):
    """Get all organizations for a user. Requires system admin access."""
    try:
        organizations = await user_service.get_user_organizations(user_id)
        
        return {
            "user_id": user_id,
            "organizations": [
                {
                    "organization": org["organization"].dict(),
                    "role": org["role"].dict() if org["role"] else None,
                    "joined_at": org["joined_at"].isoformat(),
                    "is_active": org["is_active"]
                }
                for org in organizations
            ],
            "total_organizations": len(organizations)
        }
        
    except DatabaseError as e:
        logger.error(f"Failed to get user organizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user organizations"
        )
