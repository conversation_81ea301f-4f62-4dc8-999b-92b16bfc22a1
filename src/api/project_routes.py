"""Rutas de API para gestión de proyectos."""

from typing import List
from fastapi import APIRouter, HTTPException, Depends, Query, File, UploadFile, Request
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from src.core.test_service import TestService
from src.api.models import (
    ProjectCreateRequest,
    ProjectUpdateRequest,
    ProjectResponse,
    SuccessResponse,
    ErrorResponse,
    ListResponse
)
import os
from src.services.project_service import ProjectService
from src.core.auth_middleware import get_current_user, get_current_user_id, get_current_org_id
from src.core.organization_middleware import get_org_context, org_query_helper
from src.core.role_middleware import require_write_permission, require_delete_permission
from src.database.models.user import User

# Router para proyectos
router = APIRouter(tags=["Projects"])


def get_test_service():
    """Crea y devuelve una instancia del servicio de pruebas."""
    return TestService(api_key=os.environ.get("GOOGLE_API_KEY"))


def get_project_service():
    return ProjectService()


@router.post("/", response_model=ProjectResponse, summary="Crear proyecto")
async def create_project(
    request: ProjectCreateRequest,
    test_service: TestService = Depends(get_test_service),
    current_user: User = Depends(get_current_user),
    user_id: str = Depends(get_current_user_id),
    org_id: str = Depends(get_current_org_id),
    _: bool = Depends(require_write_permission)
):
    """Crea un nuevo proyecto."""
    try:
        # Convertir la configuración de GitHub a diccionario si existe
        github_config_dict = request.github_config.dict() if request.github_config else None

        project_data = await test_service.create_project(
            name=request.name,
            description=request.description,
            tags=request.tags,
            github_config=github_config_dict,
            organization_id=org_id,
            created_by=user_id
        )
        return ProjectResponse(**project_data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/", response_model=ListResponse, summary="Listar proyectos")
async def get_all_projects(
    test_service: TestService = Depends(get_test_service),
    current_user: User = Depends(get_current_user),
    org_id: str = Depends(get_current_org_id),
    org_context: dict = Depends(get_org_context)
):
    """Obtiene todos los proyectos de la organización del usuario."""
    try:
        # Filter projects by organization
        projects = await test_service.get_all_projects(organization_id=org_id)
        return ListResponse(
            count=len(projects),
            items=projects
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}", response_model=ProjectResponse, summary="Obtener proyecto")
async def get_project(
    project_id: str,
    test_service: TestService = Depends(get_test_service),
    current_user: User = Depends(get_current_user),
    org_id: str = Depends(get_current_org_id),
    org_context: dict = Depends(get_org_context)
):
    """Obtiene un proyecto por su ID."""
    try:
        project_data = await test_service.get_project(project_id, organization_id=org_id)
        if not project_data:
            raise HTTPException(status_code=404, detail="Proyecto no encontrado")

        return ProjectResponse(**project_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{project_id}", response_model=ProjectResponse, summary="Actualizar proyecto")
async def update_project(
    project_id: str,
    request: ProjectUpdateRequest,
    test_service: TestService = Depends(get_test_service),
    current_user: User = Depends(get_current_user),
    org_id: str = Depends(get_current_org_id),
    _: bool = Depends(require_write_permission)
):
    """Actualiza un proyecto existente."""
    try:
        # Convertir la configuración de GitHub a diccionario si existe
        github_config_dict = request.github_config.dict() if request.github_config else None

        project_data = await test_service.update_project(
            project_id=project_id,
            name=request.name,
            description=request.description,
            tags=request.tags,
            github_config=github_config_dict,
            organization_id=org_id
        )
        if not project_data:
            raise HTTPException(status_code=404, detail="Proyecto no encontrado")

        return ProjectResponse(**project_data)
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{project_id}", response_model=SuccessResponse, summary="Eliminar proyecto")
async def delete_project(
    project_id: str,
    test_service: TestService = Depends(get_test_service),
    current_user: User = Depends(get_current_user),
    org_id: str = Depends(get_current_org_id),
    _: bool = Depends(require_delete_permission)
):
    """Elimina un proyecto."""
    try:
        success = await test_service.delete_project(project_id, organization_id=org_id)
        if not success:
            raise HTTPException(status_code=404, detail="Proyecto no encontrado")

        return SuccessResponse(message="Proyecto eliminado exitosamente")
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === RUTAS PARA SUITES DE PRUEBAS ===

@router.get("/{project_id}/suites", response_model=ListResponse, summary="Listar suites del proyecto")
async def get_project_suites(
    project_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Obtiene todas las suites de pruebas de un proyecto."""
    try:
        suites = await test_service.get_project_suites(project_id)
        return ListResponse(
            count=len(suites),
            items=suites
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === RUTAS PARA CASOS DE PRUEBA ===

@router.get("/{project_id}/suites/{suite_id}/tests", response_model=ListResponse,
           summary="Listar casos de prueba de la suite")
async def get_suite_test_cases(
    project_id: str,
    suite_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Obtiene todos los casos de prueba de una suite."""
    try:
        test_cases = await test_service.get_suite_test_cases(project_id, suite_id)
        return ListResponse(
            count=len(test_cases),
            items=test_cases
        )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


# === RUTAS PARA EJECUCIÓN ===

@router.post("/{project_id}/suites/{suite_id}/execute", summary="Ejecutar suite completa", operation_id="execute_project_suite")
async def execute_test_suite(
    project_id: str,
    suite_id: str,
    test_service: TestService = Depends(get_test_service)
):
    """Ejecuta todos los casos de prueba de una suite."""
    try:
        result = await test_service.execute_test_suite(project_id, suite_id)

        # Importar las funciones de transformación desde response_transformers
        from src.utilities.response_transformers import clean_data_for_json_serialization

        # Para suites, solo aplicamos limpieza JSON ya que la estructura es diferente
        # (contiene múltiples resultados de casos de prueba)
        cleaned_result = clean_data_for_json_serialization(result)

        return JSONResponse(content=cleaned_result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


class TestCaseExecutionRequest(BaseModel):
    execution_times: int = Field(1, gt=0, description="Número de veces que se ejecutará el caso de prueba")


@router.post("/{project_id}/suites/{suite_id}/tests/{test_id}/execute",
            summary="Ejecutar caso de prueba", operation_id="execute_project_test_case")
async def execute_test_case(
    project_id: str,
    suite_id: str,
    test_id: str,
    request: TestCaseExecutionRequest | None = None,
    test_service: TestService = Depends(get_test_service)
):
    """Ejecuta un caso de prueba específico."""
    try:
        # Determinar cuántas veces ejecutar el test (por defecto 1)
        execution_times = request.execution_times if request else 1

        all_results = []
        for _ in range(execution_times):
            result_single = await test_service.execute_test_case(project_id, suite_id, test_id)
            all_results.append(result_single)

        # Tomar el último resultado como principal
        result = all_results[-1] if all_results else {"success": False, "error": "No se ejecutó el test"}

        # Importar las funciones de transformación desde response_transformers
        from src.utilities.response_transformers import transform_backend_response_to_frontend_format, clean_data_for_json_serialization

        # Transformar la respuesta al formato esperado por el frontend
        transformed_result = transform_backend_response_to_frontend_format(result)

        # Limpiar los datos para serialización JSON
        cleaned_result = clean_data_for_json_serialization(transformed_result)

        return JSONResponse(content=cleaned_result)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{project_id}/export")
async def export_project(project_id: str, file_path: str | None = Query(None, description="Optional file path to save the exported JSON on server"), project_service: ProjectService = Depends(get_project_service)):
    """
    Export a project to a JSON file.
    """
    return await project_service.export_project(project_id, file_path)

@router.post("/import")
async def import_project(file: UploadFile = File(...), project_service: ProjectService = Depends(get_project_service)):
    """
    Import a project from a JSON file.
    """
    file_path = f"/tmp/{file.filename}"
    with open(file_path, "wb") as f:
        f.write(await file.read())
    return await project_service.import_project(file_path)

@router.get("/{project_id}/suites/{suite_id}/export")
async def export_test_suite(project_id: str, suite_id: str, file_path: str = Query(..., description="File path to save the exported JSON"), project_service: ProjectService = Depends(get_project_service)):
    """
    Export a test suite to a JSON file.
    """
    return await project_service.export_test_suite(project_id, suite_id, file_path)

@router.post("/{project_id}/suites/import")
async def import_test_suite(project_id: str, file: UploadFile = File(...), project_service: ProjectService = Depends(get_project_service)):
    """
    Import a test suite from a JSON file into a project.
    """
    file_path = f"/tmp/{file.filename}"
    with open(file_path, "wb") as f:
        f.write(await file.read())
    return await project_service.import_test_suite(project_id, file_path)

@router.get("/{project_id}/suites/{suite_id}/tests/{test_id}/export")
async def export_test_case(project_id: str, suite_id: str, test_id: str, file_path: str = Query(..., description="File path to save the exported JSON"), project_service: ProjectService = Depends(get_project_service)):
    """
    Export a test case to a JSON file.
    """
    return await project_service.export_test_case(project_id, suite_id, test_id, file_path)

@router.post("/{project_id}/suites/{suite_id}/tests/import")
async def import_test_case(project_id: str, suite_id: str, file: UploadFile = File(...), project_service: ProjectService = Depends(get_project_service)):
    """
    Import a test case from a JSON file into a test suite.
    """
    file_path = f"/tmp/{file.filename}"
    with open(file_path, "wb") as f:
        f.write(await file.read())
    return await project_service.import_test_case(project_id, suite_id, file_path)
