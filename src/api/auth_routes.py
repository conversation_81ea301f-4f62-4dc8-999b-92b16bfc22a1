"""
Authentication API Routes for QAK Multi-Tenant System

Handles user registration, login, logout, token refresh, and profile management.
"""

import logging
from typing import Optional
from fastapi import APIRouter, HTTPException, status, Depends, Request
from pydantic import BaseModel, EmailStr, Field

from src.services.auth_service import AuthService
from src.services.user_service import UserService
from src.core.auth_middleware import get_current_user, get_current_user_id, get_token_from_request
from src.database.models.user import User
from src.database.exceptions import DatabaseError, DocumentNotFoundError

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/auth", tags=["Authentication"])

# Initialize services
auth_service = AuthService()
user_service = UserService()


# Request/Response Models

class RegisterRequest(BaseModel):
    """User registration request."""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., min_length=8, description="User password (minimum 8 characters)")
    full_name: str = Field(..., min_length=1, description="User's full name")
    organization_name: Optional[str] = Field(None, description="Organization name (optional)")


class LoginRequest(BaseModel):
    """User login request."""
    email: EmailStr = Field(..., description="User email address")
    password: str = Field(..., description="User password")


class RefreshTokenRequest(BaseModel):
    """Token refresh request."""
    refresh_token: str = Field(..., description="Refresh token")


class ChangePasswordRequest(BaseModel):
    """Change password request."""
    old_password: str = Field(..., description="Current password")
    new_password: str = Field(..., min_length=8, description="New password (minimum 8 characters)")


class UpdateProfileRequest(BaseModel):
    """Update profile request."""
    full_name: Optional[str] = Field(None, min_length=1, description="User's full name")
    email: Optional[EmailStr] = Field(None, description="User email address")


class AuthResponse(BaseModel):
    """Authentication response."""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: dict
    organization_id: Optional[str] = None


class UserProfileResponse(BaseModel):
    """User profile response."""
    user: dict
    organizations: list
    total_organizations: int
    active_organizations: int


# Authentication Endpoints

@router.post("/register", response_model=dict, status_code=status.HTTP_201_CREATED)
async def register(request: RegisterRequest):
    """Register a new user and optionally create an organization."""
    try:
        result = await auth_service.register_user(
            email=request.email,
            password=request.password,
            full_name=request.full_name,
            org_name=request.organization_name
        )
        
        logger.info(f"User registered successfully: {request.email}")
        
        return {
            "message": "User registered successfully",
            "user_id": result["user"].user_id,
            "organization_id": result["organization"].org_id if result["organization"] else None
        }
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Registration failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Registration failed"
        )


@router.post("/login", response_model=AuthResponse)
async def login(request: LoginRequest):
    """Authenticate user and return access tokens."""
    try:
        result = await auth_service.login_user(
            email=request.email,
            password=request.password
        )
        
        logger.info(f"User logged in successfully: {request.email}")
        
        return AuthResponse(
            access_token=result["access_token"],
            refresh_token=result["refresh_token"],
            expires_in=result["expires_in"],
            user=result["user"].dict(),
            organization_id=result["organization_id"]
        )
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Login failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Login failed"
        )


@router.post("/logout")
async def logout(request: Request):
    """Logout user by blacklisting the current token."""
    try:
        token = get_token_from_request(request)
        if not token:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No token provided"
            )
        
        success = auth_service.logout_user(token)
        
        if success:
            logger.info("User logged out successfully")
            return {"message": "Logged out successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Logout failed"
            )
            
    except Exception as e:
        logger.error(f"Logout failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Logout failed"
        )


@router.post("/refresh")
async def refresh_token(request: RefreshTokenRequest):
    """Refresh access token using refresh token."""
    try:
        new_access_token = auth_service.refresh_token(request.refresh_token)
        
        return {
            "access_token": new_access_token,
            "token_type": "bearer",
            "expires_in": 30 * 60  # 30 minutes
        }
        
    except Exception as e:
        logger.error(f"Token refresh failed: {e}")
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )


# Profile Management Endpoints

@router.get("/me", response_model=UserProfileResponse)
async def get_profile(
    current_user: User = Depends(get_current_user),
    user_id: str = Depends(get_current_user_id)
):
    """Get current user profile with organizations."""
    try:
        profile = await user_service.get_user_profile(user_id)
        
        return UserProfileResponse(
            user=profile["user"].dict(),
            organizations=[
                {
                    "organization": org["organization"].dict(),
                    "role": org["role"].dict() if org["role"] else None,
                    "joined_at": org["joined_at"],
                    "is_active": org["is_active"]
                }
                for org in profile["organizations"]
            ],
            total_organizations=profile["total_organizations"],
            active_organizations=profile["active_organizations"]
        )
        
    except DocumentNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    except DatabaseError as e:
        logger.error(f"Failed to get user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get user profile"
        )


@router.put("/me")
async def update_profile(
    request: UpdateProfileRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Update current user profile."""
    try:
        # Filter out None values
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No data provided for update"
            )
        
        updated_user = await user_service.update_user_profile(user_id, update_data)
        
        logger.info(f"User profile updated: {user_id}")
        
        return {
            "message": "Profile updated successfully",
            "user": updated_user.dict()
        }
        
    except DocumentNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Failed to update user profile: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update profile"
        )


@router.post("/change-password")
async def change_password(
    request: ChangePasswordRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Change user password."""
    try:
        success = await user_service.change_password(
            user_id=user_id,
            old_password=request.old_password,
            new_password=request.new_password
        )
        
        if success:
            logger.info(f"Password changed for user: {user_id}")
            return {"message": "Password changed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to change password"
            )
            
    except DocumentNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Failed to change password: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change password"
        )
