"""
Organization API Routes for QAK Multi-Tenant System

Handles organization management, member management, and role assignments.
"""

import logging
from typing import Optional, List
from fastapi import APIRouter, HTTPException, status, Depends, Request
from pydantic import BaseModel, EmailStr, Field

from src.services.organization_service import OrganizationService
from src.core.auth_middleware import get_current_user, get_current_user_id, get_current_org_id
from src.core.role_middleware import require_org_admin, require_system_admin
from src.database.models.organization import Organization, PlanType
from src.database.models.role import RoleName
from src.database.exceptions import DatabaseError, DocumentNotFoundError

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/organizations", tags=["Organizations"])

# Initialize services
org_service = OrganizationService()


# Request/Response Models

class CreateOrganizationRequest(BaseModel):
    """Create organization request."""
    name: str = Field(..., min_length=1, description="Organization name")
    description: Optional[str] = Field("", description="Organization description")


class UpdateOrganizationRequest(BaseModel):
    """Update organization request."""
    name: Optional[str] = Field(None, min_length=1, description="Organization name")
    description: Optional[str] = Field(None, description="Organization description")
    plan_type: Optional[PlanType] = Field(None, description="Organization plan type")


class AddMemberRequest(BaseModel):
    """Add member to organization request."""
    email: EmailStr = Field(..., description="User email address")
    role: RoleName = Field(default=RoleName.USER, description="User role in organization")


class ChangeMemberRoleRequest(BaseModel):
    """Change member role request."""
    role: RoleName = Field(..., description="New role for the member")


class OrganizationResponse(BaseModel):
    """Organization response."""
    organization: dict
    member_count: int
    user_role: Optional[str] = None


class MemberResponse(BaseModel):
    """Organization member response."""
    user: dict
    role: dict
    joined_at: str
    is_active: bool


# Organization Management Endpoints

@router.get("", response_model=List[OrganizationResponse])
async def get_user_organizations(
    user_id: str = Depends(get_current_user_id)
):
    """Get all organizations for the current user."""
    try:
        from src.services.user_service import UserService
        user_service = UserService()
        
        organizations = await user_service.get_user_organizations(user_id)
        
        response = []
        for org_data in organizations:
            # Get member count
            members = await org_service.get_organization_members(org_data["organization"].org_id)
            
            response.append(OrganizationResponse(
                organization=org_data["organization"].dict(),
                member_count=len(members),
                user_role=org_data["role"].name.value if org_data["role"] else None
            ))
        
        return response
        
    except DatabaseError as e:
        logger.error(f"Failed to get user organizations: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get organizations"
        )


@router.post("", response_model=dict, status_code=status.HTTP_201_CREATED)
async def create_organization(
    request: CreateOrganizationRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Create a new organization."""
    try:
        organization = await org_service.create_organization(
            name=request.name,
            created_by=user_id,
            description=request.description
        )
        
        logger.info(f"Organization created: {organization.org_id} by user: {user_id}")
        
        return {
            "message": "Organization created successfully",
            "organization": organization.dict()
        }
        
    except DocumentNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Failed to create organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create organization"
        )


@router.get("/{org_id}", response_model=dict)
async def get_organization(
    org_id: str,
    user_id: str = Depends(get_current_user_id)
):
    """Get organization details."""
    try:
        # Verify user has access to this organization
        from src.database.repositories.organization_member_repository import OrganizationMemberRepository
        member_repo = OrganizationMemberRepository()
        
        is_member = await member_repo.is_user_member(user_id, org_id)
        if not is_member:
            # Check if user is system admin
            from src.core.role_middleware import role_checker
            is_admin = await role_checker.is_system_admin(user_id)
            if not is_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this organization"
                )
        
        organization = await org_service.get_organization(org_id)
        if not organization:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Organization not found"
            )
        
        # Get organization statistics
        stats = await org_service.get_organization_stats(org_id)
        
        return {
            "organization": organization.dict(),
            "stats": stats
        }
        
    except HTTPException:
        raise
    except DatabaseError as e:
        logger.error(f"Failed to get organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get organization"
        )


@router.put("/{org_id}")
async def update_organization(
    org_id: str,
    request: UpdateOrganizationRequest,
    user_id: str = Depends(get_current_user_id),
    _: bool = Depends(require_org_admin())
):
    """Update organization information. Requires ORG_ADMIN role."""
    try:
        # Verify user has admin access to this organization
        is_admin = await org_service.is_user_org_admin(user_id, org_id)
        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Organization admin access required"
            )
        
        # Filter out None values
        update_data = {k: v for k, v in request.dict().items() if v is not None}
        
        if not update_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="No data provided for update"
            )
        
        updated_org = await org_service.update_organization(org_id, update_data)
        
        logger.info(f"Organization updated: {org_id} by user: {user_id}")
        
        return {
            "message": "Organization updated successfully",
            "organization": updated_org.dict()
        }
        
    except HTTPException:
        raise
    except DocumentNotFoundError:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Organization not found"
        )
    except DatabaseError as e:
        logger.error(f"Failed to update organization: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update organization"
        )


# Member Management Endpoints

@router.get("/{org_id}/members", response_model=List[MemberResponse])
async def get_organization_members(
    org_id: str,
    user_id: str = Depends(get_current_user_id)
):
    """Get all members of an organization."""
    try:
        # Verify user has access to this organization
        from src.database.repositories.organization_member_repository import OrganizationMemberRepository
        member_repo = OrganizationMemberRepository()
        
        is_member = await member_repo.is_user_member(user_id, org_id)
        if not is_member:
            # Check if user is system admin
            from src.core.role_middleware import role_checker
            is_admin = await role_checker.is_system_admin(user_id)
            if not is_admin:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail="Access denied to this organization"
                )
        
        members = await org_service.get_organization_members(org_id)
        
        response = []
        for member_data in members:
            response.append(MemberResponse(
                user=member_data["user"].dict(),
                role=member_data["role"].dict() if member_data["role"] else {},
                joined_at=member_data["joined_at"].isoformat(),
                is_active=member_data["is_active"]
            ))
        
        return response
        
    except HTTPException:
        raise
    except DatabaseError as e:
        logger.error(f"Failed to get organization members: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get organization members"
        )


@router.post("/{org_id}/members", status_code=status.HTTP_201_CREATED)
async def add_member(
    org_id: str,
    request: AddMemberRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Add a new member to the organization. Requires ORG_ADMIN role."""
    try:
        # Verify user has admin access to this organization
        is_admin = await org_service.is_user_org_admin(user_id, org_id)
        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Organization admin access required"
            )
        
        membership = await org_service.add_member(
            org_id=org_id,
            email=request.email,
            role=request.role.value,
            invited_by=user_id
        )
        
        logger.info(f"Member added to organization {org_id}: {request.email}")
        
        return {
            "message": "Member added successfully",
            "membership": {
                "member_id": membership.member_id,
                "user_id": membership.user_id,
                "role_id": membership.role_id,
                "joined_at": membership.joined_at.isoformat()
            }
        }
        
    except HTTPException:
        raise
    except (DocumentNotFoundError, ValueError) as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Failed to add member: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to add member"
        )


@router.delete("/{org_id}/members/{member_user_id}")
async def remove_member(
    org_id: str,
    member_user_id: str,
    user_id: str = Depends(get_current_user_id)
):
    """Remove a member from the organization. Requires ORG_ADMIN role."""
    try:
        # Verify user has admin access to this organization
        is_admin = await org_service.is_user_org_admin(user_id, org_id)
        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Organization admin access required"
            )

        # Prevent self-removal
        if user_id == member_user_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot remove yourself from the organization"
            )

        success = await org_service.remove_member(org_id, member_user_id)

        if success:
            logger.info(f"Member removed from organization {org_id}: {member_user_id}")
            return {"message": "Member removed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to remove member"
            )

    except HTTPException:
        raise
    except DocumentNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Failed to remove member: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to remove member"
        )


@router.put("/{org_id}/members/{member_user_id}/role")
async def change_member_role(
    org_id: str,
    member_user_id: str,
    request: ChangeMemberRoleRequest,
    user_id: str = Depends(get_current_user_id)
):
    """Change a member's role in the organization. Requires ORG_ADMIN role."""
    try:
        # Verify user has admin access to this organization
        is_admin = await org_service.is_user_org_admin(user_id, org_id)
        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Organization admin access required"
            )

        # Prevent changing own role to prevent lockout
        if user_id == member_user_id and request.role != RoleName.ORG_ADMIN:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Cannot change your own admin role"
            )

        success = await org_service.change_member_role(
            org_id=org_id,
            user_id=member_user_id,
            new_role=request.role.value
        )

        if success:
            logger.info(f"Member role changed in organization {org_id}: {member_user_id} -> {request.role.value}")
            return {"message": "Member role changed successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to change member role"
            )

    except HTTPException:
        raise
    except DocumentNotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except DatabaseError as e:
        logger.error(f"Failed to change member role: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to change member role"
        )
