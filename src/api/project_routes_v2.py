"""
Project API Routes with Multi-tenant Authentication

Updated project routes that include authentication and organization context.
This demonstrates how to integrate the authentication system with existing endpoints.
"""

import logging
from typing import List, Optional
from fastapi import APIRouter, HTTPException, Depends, Query, Request, status
from pydantic import BaseModel, Field

from src.services.project_service import ProjectService
from src.core.auth_middleware import get_current_user, get_current_user_id, get_current_org_id
from src.core.role_middleware import require_write_permission, require_delete_permission
from src.core.organization_middleware import get_org_context, org_query_helper
from src.api.models import (
    ProjectCreateRequest,
    ProjectUpdateRequest,
    ProjectResponse,
    SuccessResponse,
    ListResponse
)

logger = logging.getLogger(__name__)

# Router for authenticated project operations
router = APIRouter(prefix="/api/v2/projects", tags=["Projects V2 (Authenticated)"])


def get_project_service():
    """Get ProjectService instance."""
    return ProjectService()


# Request/Response Models for V2

class ProjectCreateRequestV2(BaseModel):
    """Project creation request for V2 API."""
    name: str = Field(..., min_length=1, description="Project name")
    description: str = Field("", description="Project description")
    tags: List[str] = Field(default_factory=list, description="Project tags")
    github_config: Optional[dict] = Field(None, description="GitHub configuration")


class ProjectListResponse(BaseModel):
    """Project list response with pagination."""
    projects: List[dict]
    pagination: dict
    organization_context: dict


# Authenticated Project Endpoints

@router.post("", response_model=dict, status_code=status.HTTP_201_CREATED)
async def create_project(
    request: ProjectCreateRequestV2,
    fastapi_request: Request,
    user_id: str = Depends(get_current_user_id),
    org_id: str = Depends(get_current_org_id),
    _: bool = Depends(require_write_permission()),
    project_service: ProjectService = Depends(get_project_service)
):
    """Create a new project with authentication and organization context."""
    try:
        # Use the convenience method that extracts context from request
        result = await project_service.create_project_with_context(
            name=request.name,
            description=request.description,
            tags=request.tags,
            github_config=request.github_config,
            request_context=fastapi_request
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.error_message or "Failed to create project"
            )
        
        project = result.data
        logger.info(f"Project created: {project.project_id} by user: {user_id} in org: {org_id}")
        
        return {
            "message": "Project created successfully",
            "project": project.dict(),
            "organization_id": org_id,
            "created_by": user_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to create project: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to create project"
        )


@router.get("", response_model=ProjectListResponse)
async def list_projects(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    tags: Optional[List[str]] = Query(None, description="Filter by tags"),
    search: Optional[str] = Query(None, description="Search in name and description"),
    user_id: str = Depends(get_current_user_id),
    org_id: str = Depends(get_current_org_id),
    org_context: dict = Depends(get_org_context),
    project_service: ProjectService = Depends(get_project_service)
):
    """List projects with organization filtering and pagination."""
    try:
        # Use organization-aware list method
        result = await project_service.list_projects(
            tags=tags,
            search_term=search,
            page=page,
            page_size=page_size,
            organization_id=org_id,
            user_id=user_id if not org_context["can_access_all_orgs"] else None
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.error_message or "Failed to list projects"
            )
        
        data = result.data
        
        return ProjectListResponse(
            projects=[project.dict() for project in data["projects"]],
            pagination=data["pagination"],
            organization_context={
                "organization_id": org_id,
                "can_access_all_orgs": org_context["can_access_all_orgs"],
                "is_system_admin": org_context["is_system_admin"]
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list projects: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to list projects"
        )


@router.get("/{project_id}", response_model=dict)
async def get_project(
    project_id: str,
    user_id: str = Depends(get_current_user_id),
    org_id: str = Depends(get_current_org_id),
    org_context: dict = Depends(get_org_context),
    project_service: ProjectService = Depends(get_project_service)
):
    """Get a project by ID with organization filtering."""
    try:
        # Use organization-aware get method
        result = await project_service.get_project(
            project_id=project_id,
            organization_id=org_id if not org_context["can_access_all_orgs"] else None
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.error_message or "Failed to get project"
            )
        
        project = result.data
        if not project:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found"
            )
        
        return {
            "project": project.dict(),
            "organization_context": {
                "organization_id": org_id,
                "can_access_all_orgs": org_context["can_access_all_orgs"]
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get project {project_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get project"
        )


@router.put("/{project_id}", response_model=dict)
async def update_project(
    project_id: str,
    request: ProjectUpdateRequest,
    user_id: str = Depends(get_current_user_id),
    org_id: str = Depends(get_current_org_id),
    _: bool = Depends(require_write_permission()),
    project_service: ProjectService = Depends(get_project_service)
):
    """Update a project with organization security."""
    try:
        # First verify the project exists and user has access
        get_result = await project_service.get_project(project_id, org_id)
        if not get_result.success or not get_result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found or access denied"
            )
        
        # Update the project
        result = await project_service.update_project(
            project_id=project_id,
            name=request.name,
            description=request.description,
            tags=request.tags,
            github_config=request.github_config.dict() if request.github_config else None
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.error_message or "Failed to update project"
            )
        
        project = result.data
        logger.info(f"Project updated: {project_id} by user: {user_id}")
        
        return {
            "message": "Project updated successfully",
            "project": project.dict()
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to update project {project_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to update project"
        )


@router.delete("/{project_id}", response_model=dict)
async def delete_project(
    project_id: str,
    user_id: str = Depends(get_current_user_id),
    org_id: str = Depends(get_current_org_id),
    _: bool = Depends(require_delete_permission()),
    project_service: ProjectService = Depends(get_project_service)
):
    """Delete a project with organization security."""
    try:
        # Use organization-aware delete method
        result = await project_service.delete_project(
            project_id=project_id,
            organization_id=org_id
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.error_message or "Failed to delete project"
            )
        
        if not result.data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Project not found or access denied"
            )
        
        logger.info(f"Project deleted: {project_id} by user: {user_id}")
        
        return {
            "message": "Project deleted successfully",
            "project_id": project_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to delete project {project_id}: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete project"
        )
