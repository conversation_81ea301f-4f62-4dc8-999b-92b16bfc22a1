import logging
"""Extensiones del TestService para gestión de suites y casos de pruebas."""

import asyncio
from typing import Dict, Any, Optional, List
from datetime import datetime

# Configurar path para browser_use local
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../../libs'))

from browser_use import BrowserSession
from src.utilities.browser_helper import get_browser_session_with_cleanup
from src.config.browser_config import get_config_by_type
from src.core.execution_orchestrator import ExecutionOrchestrator
from src.api.v2.execution_routes import TestCaseRequest
from src.models.standard_result import StandardResult


class TestServiceSuitesMixin:
    """Mixin para gestión de suites de pruebas."""

    async def create_test_suite(self, project_id: str, name: str, description: str = "",
                         tags: List[str] = None, execution_times: int = 1) -> Optional[Dict[str, Any]]:
        """Crea una nueva suite de pruebas."""
        suite = await self.project_manager.create_test_suite(
            project_id=project_id, name=name, description=description, tags=tags or [], execution_times=execution_times
        )
        return suite.to_dict() if suite else None

    async def get_test_suite(self, project_id: str, suite_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene una suite de pruebas por su ID."""
        suite = await self.project_manager.get_test_suite(project_id, suite_id)
        return suite.to_dict() if suite else None

    async def get_project_suites(self, project_id: str) -> List[Dict[str, Any]]:
        """Obtiene todas las suites de un proyecto."""
        project = await self.project_manager.get_project(project_id)
        if not project:
            return []

        suites = project.get_all_test_suites()
        return [suite.to_dict() for suite in suites]

    async def update_test_suite(self, project_id: str, suite_id: str, name: str = None,
                         description: str = None, tags: List[str] = None, execution_times: int = None) -> Optional[Dict[str, Any]]:
        """Actualiza una suite de pruebas existente."""
        suite = await self.project_manager.update_test_suite(
            project_id=project_id, suite_id=suite_id, name=name,
            description=description, tags=tags, execution_times=execution_times
        )
        return suite.to_dict() if suite else None

    async def delete_test_suite(self, project_id: str, suite_id: str) -> bool:
        """Elimina una suite de pruebas."""
        return await self.project_manager.delete_test_suite(project_id, suite_id)


class TestServiceTestCasesMixin:
    """Mixin para gestión de casos de prueba."""

    async def create_test_case(self, project_id: str, suite_id: str, name: str, description: str = "",
                        instrucciones: str = "", historia_de_usuario: str = "",
                        gherkin: str = "", url: str = "", tags: List[str] = None) -> Optional[Dict[str, Any]]:
        """Crea un nuevo caso de prueba."""
        test_case = await self.project_manager.create_test_case(
            project_id=project_id, suite_id=suite_id, name=name, description=description,
            instrucciones=instrucciones, historia_de_usuario=historia_de_usuario,
            gherkin=gherkin, url=url, tags=tags or []
        )
        return test_case.to_dict() if test_case else None

    async def get_test_case(self, project_id: str, suite_id: str, test_id: str) -> Optional[Dict[str, Any]]:
        """Obtiene un caso de prueba por su ID."""
        test_case = await self.project_manager.get_test_case(project_id, suite_id, test_id)
        return test_case.to_dict() if test_case else None

    async def get_suite_test_cases(self, project_id: str, suite_id: str) -> List[Dict[str, Any]]:
        """Obtiene todos los casos de prueba de una suite."""
        suite = await self.project_manager.get_test_suite(project_id, suite_id)
        if not suite:
            return []

        test_cases = suite.get_all_test_cases()
        return [test_case.to_dict() for test_case in test_cases]

    async def update_test_case(self, project_id: str, suite_id: str, test_id: str,
                        name: str = None, description: str = None,
                        instrucciones: str = None, historia_de_usuario: str = None,
                        gherkin: str = None, url: str = None,
                        tags: List[str] = None) -> Optional[Dict[str, Any]]:
        """Actualiza un caso de prueba existente."""
        test_case = await self.project_manager.update_test_case(
            project_id=project_id, suite_id=suite_id, test_id=test_id,
            name=name, description=description, instrucciones=instrucciones,
            historia_de_usuario=historia_de_usuario, gherkin=gherkin, url=url, tags=tags
        )
        return test_case.to_dict() if test_case else None

    async def delete_test_case(self, project_id: str, suite_id: str, test_id: str) -> bool:
        """Elimina un caso de prueba."""
        return await self.project_manager.delete_test_case(project_id, suite_id, test_id)

    async def update_test_case_status(self, project_id: str, suite_id: str, test_id: str,
                               status: str) -> Optional[Dict[str, Any]]:
        """Actualiza el estado de un caso de prueba."""
        test_case = await self.project_manager.update_test_case_status(project_id, suite_id, test_id, status)
        return test_case.to_dict() if test_case else None


class TestServiceExecutionMixin:
    """Mixin para ejecución de suites y casos de prueba."""

    async def execute_test_case(self, project_id: str, suite_id: str, test_id: str) -> Dict[str, Any]:
        """Ejecuta un caso de prueba específico.

        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            test_id: ID del caso de prueba

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        test_case = self.project_manager.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return {"success": False, "error": "Caso de prueba no encontrado"}

        result = None
        try:
            # Usar ExecutionOrchestrator con V2 API para ejecutar el test case
            orchestrator = ExecutionOrchestrator()
            
            # Crear request compatible con V2 API
            request = TestCaseRequest(
                test_case_id=test_id,
                gherkin_scenario=test_case.gherkin or "",
                url=test_case.url or "",
                test_instructions=test_case.instrucciones or "",
                metadata={
                    "project_id": project_id,
                    "suite_id": suite_id,
                    "test_name": test_case.name,
                    "test_description": test_case.description or "",
                    "execution_times": getattr(test_case, 'execution_times', 1)
                }
            )
            
            # Ejecutar usando el orquestador
            execution_id = await orchestrator.submit_execution_request(request)
            
            # Esperar a que complete la ejecución
            execution = await orchestrator.get_execution_by_id(execution_id)
            standard_result: StandardResult = execution.result
            
            # Convertir StandardResult al formato legacy para compatibilidad
            result = {
                "success": standard_result.success,
                "error": standard_result.error_message if not standard_result.success else None,
                "test_id": execution_id,
                "history": None,  # Legacy field
                "screenshot_paths": [a.relative_path for a in standard_result.artifacts if a.artifact_type == "screenshot"],
                "history_path": None  # Legacy field
            }

            # Actualizar el estado del caso de prueba
            status = "Passed" if result.get("success", False) else "Failed"
            self.project_manager.update_test_case_status(project_id, suite_id, test_id, status)

            return result

        except Exception as e:
            # Actualizar estado a fallido
            self.project_manager.update_test_case_status(project_id, suite_id, test_id, "Failed")

            # Asegurar que devolvemos un resultado válido incluso en caso de excepción
            error_result = {
                "success": False,
                "error": str(e),
                "test_id": test_id,
                "history": None,
                "screenshot_paths": [],
                "history_path": None
            }

            return error_result

    async def execute_test_case_with_config(self, project_id: str, suite_id: str, test_id: str,
                                            config_id: Optional[str] = None,
                                            browser_session: Optional[BrowserSession] = None) -> Dict[str, Any]:
        """Ejecuta un caso de prueba específico con configuración personalizada.

        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite
            test_id: ID del caso de prueba
            config_id: ID de configuración a usar
            browser_session: Sesión de navegador existente para reutilizar (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución
        """
        test_case = self.project_manager.get_test_case(project_id, suite_id, test_id)
        if not test_case:
            return {"success": False, "error": "Caso de prueba no encontrado"}

        result = None
        try:
            # Usar ExecutionOrchestrator con V2 API para ejecutar el test case con configuración
            orchestrator = ExecutionOrchestrator()
            
            # Crear request compatible con V2 API
            request = TestCaseRequest(
                test_case_id=test_id,
                gherkin_scenario=test_case.gherkin or "",
                url=test_case.url or "",
                test_instructions=test_case.instrucciones or "",
                metadata={
                    "project_id": project_id,
                    "suite_id": suite_id,
                    "test_name": test_case.name,
                    "test_description": test_case.description or "",
                    "execution_times": getattr(test_case, 'execution_times', 1),
                    "config_id": config_id,  # Incluir configuración específica
                    "browser_session": browser_session  # Incluir sesión de navegador
                }
            )
            
            # Ejecutar usando el orquestador
            execution_id = await orchestrator.submit_execution_request(request)
            
            # Esperar a que complete la ejecución
            execution = await orchestrator.get_execution_by_id(execution_id)
            standard_result: StandardResult = execution.result
            
            # Convertir StandardResult al formato legacy para compatibilidad
            result = {
                "success": standard_result.success,
                "error": standard_result.error_message if not standard_result.success else None,
                "test_id": execution_id,
                "history": None,  # Legacy field
                "screenshot_paths": [a.relative_path for a in standard_result.artifacts if a.artifact_type == "screenshot"],
                "history_path": None  # Legacy field
            }

            # Verificar que el resultado sea válido
            if result is None:
                result = {"success": False, "error": "No se recibió resultado de la ejecución"}

            # Actualizar el estado del caso de prueba
            status = "Passed" if result.get("success", False) else "Failed"
            self.project_manager.update_test_case_status(project_id, suite_id, test_id, status)

            # Agregar historial si existe
            if result.get("history_path"):
                self.project_manager.add_history_to_test_case(
                    project_id, suite_id, test_id, result["history_path"]
                )

            return result

        except Exception as e:
            # Actualizar estado a fallido
            self.project_manager.update_test_case_status(project_id, suite_id, test_id, "Failed")

            # Asegurar que devolvemos un resultado válido incluso en caso de excepción
            error_result = {
                "success": False,
                "error": str(e),
                "test_id": test_id,
                "history": None,
                "screenshot_paths": [],
                "history_path": None
            }

            return error_result

    async def execute_test_suite(self, project_id: str, suite_id: str) -> Dict[str, Any]:
        """Ejecuta todos los casos de prueba de una suite las veces especificadas en execution_times.

        Args:
            project_id: ID del proyecto
            suite_id: ID de la suite

        Returns:
            Dict[str, Any]: Resultado de la ejecución de la suite con múltiples iteraciones
        """
        suite = self.project_manager.get_test_suite(project_id, suite_id)
        if not suite:
            return {"success": False, "error": "Suite de pruebas no encontrada"}

        test_cases = suite.get_all_test_cases()
        if not test_cases:
            return {"success": False, "error": "No hay casos de prueba en la suite"}

        # Obtener el número de ejecuciones configurado
        execution_times = getattr(suite, 'execution_times', 1)
        
        all_executions = []
        total_passed = 0
        total_failed = 0
        
        logging.info(f"🔄 Ejecutando suite '{suite.name}' {execution_times} vez(es)...")

        # Crear una única sesión de navegador para todas las iteraciones y casos de prueba
        browser_session = None
        try:
            # Usar la configuración de test_suite para crear el perfil del navegador
            suite_config = get_config_by_type('test_suite')
            # Forzar keep_alive para mantener la sesión abierta durante toda la suite
            suite_config.keep_alive = True
            browser_session = get_browser_session_with_cleanup(suite_config, auto_cleanup=False)
            await browser_session.start()
            logging.info("🚀 Navegador iniciado para la suite de pruebas.")

            # Ejecutar la suite el número de veces especificado
            for execution_round in range(execution_times):
                logging.info(f"📋 Ejecutando iteración {execution_round + 1} de {execution_times}")
                
                results = []
                passed = 0
                failed = 0

                for i, test_case in enumerate(test_cases):
                    # Agregar delay entre tests para evitar límites de cuota de Gemini
                    if i > 0:  # No delay antes del primer test
                        logging.info(f"⏳ Esperando 5 segundos antes del siguiente test para evitar límites de cuota...")
                        await asyncio.sleep(5)
                        
                    # Usar configuración especializada y la sesión compartida
                    result = await self.execute_test_case_with_config(
                        project_id, suite_id, test_case.test_id, config_id="test_suite",
                        browser_session=browser_session
                    )

                    # Crear el objeto TestExecutionResponse compatible
                    test_execution_result = {
                        "success": result.get("success", False),
                        "test_id": test_case.test_id,
                        "test_name": test_case.name,
                        "result": result,
                        "error": result.get("error") if not result.get("success", False) else None,
                        "execution_time": result.get("execution_time"),
                        "execution_round": execution_round + 1
                    }

                    results.append(test_execution_result)

                    if result.get("success", False):
                        passed += 1
                        total_passed += 1
                    else:
                        failed += 1
                        total_failed += 1

                # Resultado de esta iteración
                execution_result = {
                    "execution_round": execution_round + 1,
                    "total_tests": len(test_cases),
                    "passed": passed,
                    "failed": failed,
                    "results": results,
                    "execution_time": datetime.now().isoformat()
                }
                
                all_executions.append(execution_result)
                
                # Delay entre iteraciones (excepto la última)
                if execution_round < execution_times - 1:
                    logging.info(f"⏳ Esperando 10 segundos antes de la siguiente iteración...")
                    await asyncio.sleep(10)
        
        finally:
            # Asegurarse de cerrar la sesión del navegador al final de la ejecución de la suite
            if browser_session:
                await browser_session.close()
                logging.info("🏁 Navegador cerrado al finalizar la suite de pruebas.")

        # Actualizar estadísticas de ejecución de la suite
        suite.update_execution_statistics(
            total_tests=len(test_cases),
            passed_tests=total_passed,
            failed_tests=total_failed,
            execution_rounds=execution_times
        )
        
        # Guardar el proyecto actualizado
        project = self.project_manager.get_project(project_id)
        if project:
            self.project_manager.save_project(project)

        return {
            "success": True,
            "suite_id": suite_id,
            "suite_name": suite.name,
            "execution_times": execution_times,
            "total_executions": len(all_executions),
            "total_tests_per_execution": len(test_cases),
            "total_tests_executed": len(test_cases) * execution_times,
            "total_passed": total_passed,
            "total_failed": total_failed,
            "executions": all_executions,
            "execution_time": datetime.now().isoformat(),
            "execution_statistics": suite.get_execution_summary()
        }
