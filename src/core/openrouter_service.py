"""
OpenRouter LLM Service for QAK
Optimized for /src requests: Gherkin generation, validation, translation
"""

import os
import logging
from typing import Dict, Any, List, Optional
from openrouter_client import OpenRouterClient
from openrouter_client.exceptions import AuthenticationError, RateLimitError, ValidationError

logger = logging.getLogger(__name__)

class OpenRouterService:
    """Service for OpenRouter LLM operations optimized for QAK /src use cases."""
    
    def __init__(self, api_key: Optional[str] = None):
        """Initialize OpenRouter service.
        
        Args:
            api_key: OpenRouter API key (defaults to env OPENROUTER_API_KEY)
        """
        self.api_key = api_key or os.getenv("OPENROUTER_API_KEY")
        if not self.api_key:
            raise ValueError("OpenRouter API key required. Set OPENROUTER_API_KEY env var.")
        
        # Initialize client with intelligent rate limiting
        self.client = OpenRouterClient(
            api_key=self.api_key,
            max_retries=5,  # Automatic retries on rate limits
            rate_limit_buffer=0.2,  # 20% safety buffer
            timeout=60.0  # Extended timeout for retries
        )
        
        # Model configurations for different use cases
        self.model_configs = {
            "gherkin": {
                "models": ["anthropic/claude-3-haiku:free", "openai/gpt-3.5-turbo:free"],
                "max_cost_per_1k": 0.005,
                "temperature": 0.1
            },
            "validation": {
                "models": ["openai/gpt-3.5-turbo:free", "anthropic/claude-3-haiku:free"],
                "max_cost_per_1k": 0.01,
                "temperature": 0.0
            },
            "translation": {
                "models": ["meta-llama/llama-3.1-8b:free", "openai/gpt-3.5-turbo:free"],
                "max_cost_per_1k": 0.003,
                "temperature": 0.1
            },
            "enhancement": {
                "models": ["anthropic/claude-3-sonnet:free", "openai/gpt-4o-mini:free"],
                "max_cost_per_1k": 0.008,
                "temperature": 0.2
            }
        }

    def _select_model(self, use_case: str, fallback_to_premium: bool = False) -> str:
        """Select optimal model for use case.
        
        Args:
            use_case: One of 'gherkin', 'validation', 'translation', 'enhancement'
            fallback_to_premium: If True, allows premium models as fallback
            
        Returns:
            Model ID to use
        """
        config = self.model_configs.get(use_case, self.model_configs["gherkin"])
        models = config["models"]
        
        if fallback_to_premium:
            # Add premium fallbacks
            premium_models = [
                "anthropic/claude-3-sonnet",
                "openai/gpt-4o-mini", 
                "anthropic/claude-3-haiku"
            ]
            models = models + [m for m in premium_models if m not in models]
        
        return models[0]  # Start with first preferred model

    def _make_request_with_fallback(self, messages: List[Dict[str, str]], 
                                  use_case: str, **kwargs) -> Dict[str, Any]:
        """Make request with automatic model fallback.
        
        Args:
            messages: Chat messages
            use_case: Use case type for model selection
            **kwargs: Additional parameters for chat.create
            
        Returns:
            Response content and metadata
        """
        config = self.model_configs.get(use_case, self.model_configs["gherkin"])
        models_to_try = config["models"].copy()
        
        # Add premium fallbacks if needed
        premium_fallbacks = [
            "anthropic/claude-3-sonnet",
            "openai/gpt-4o-mini",
            "anthropic/claude-3-haiku"
        ]
        models_to_try.extend([m for m in premium_fallbacks if m not in models_to_try])
        
        last_error = None
        
        for model in models_to_try:
            try:
                logger.info(f"Trying model {model} for {use_case}")
                
                response = self.client.chat.create(
                    model=model,
                    messages=messages,
                    temperature=config.get("temperature", 0.1),
                    max_tokens=kwargs.get("max_tokens", 2048),
                    **{k: v for k, v in kwargs.items() if k != "max_tokens"}
                )
                
                # Log successful request
                logger.info(f"Success with model {model} for {use_case}")
                
                return {
                    "content": response.choices[0].message.content,
                    "model_used": model,
                    "usage": response.usage.dict() if response.usage else None,
                    "success": True
                }
                
            except (AuthenticationError, ValidationError) as e:
                # Non-recoverable errors
                logger.error(f"Non-recoverable error with model {model}: {e}")
                raise
                
            except RateLimitError as e:
                # Rate limit - try next model
                logger.warning(f"Rate limited on model {model}, trying next: {e}")
                last_error = e
                continue
                
            except Exception as e:
                # Other errors - try next model
                logger.warning(f"Error with model {model}, trying next: {e}")
                last_error = e
                continue
        
        # All models failed
        raise Exception(f"All models failed for {use_case}. Last error: {last_error}")

    def generate_gherkin(self, instructions: str, user_story: str = "", 
                        url: str = "", language: str = "en") -> Dict[str, Any]:
        """Generate Gherkin scenario from instructions.
        
        Args:
            instructions: Test instructions
            user_story: Optional user story context
            url: Optional URL context
            language: Response language ('en' or 'es')
            
        Returns:
            Dict with generated Gherkin and metadata
        """
        prompt = f"""Generate a Gherkin scenario for the following:

Instructions: {instructions}
{f"User Story: {user_story}" if user_story else ""}
{f"URL: {url}" if url else ""}

Create a clear, well-structured Gherkin scenario with Given-When-Then format.
Respond in {"Spanish" if language == "es" else "English"}.
"""
        
        messages = [
            {"role": "system", "content": "You are an expert QA engineer specializing in Gherkin test scenarios."},
            {"role": "user", "content": prompt}
        ]
        
        return self._make_request_with_fallback(messages, "gherkin")

    def validate_result(self, result_data: Dict[str, Any], 
                       test_objective: str, gherkin_scenario: str = "") -> Dict[str, Any]:
        """Validate test execution result using LLM.
        
        Args:
            result_data: Test execution result data
            test_objective: What the test should accomplish
            gherkin_scenario: Optional Gherkin scenario context
            
        Returns:
            Dict with validation result and reasoning
        """
        prompt = f"""Analyze this test execution result:

Test Objective: {test_objective}
{f"Gherkin Scenario: {gherkin_scenario}" if gherkin_scenario else ""}

Result Data: {result_data}

Determine if the test actually succeeded in achieving its objective, regardless of the 
reported success status. Consider the steps taken and final state.

Respond with JSON:
{{
    "validated_success": true/false,
    "validation_confidence": 0.0-1.0,
    "validation_reasoning": "explanation",
    "should_override_result": true/false
}}
"""
        
        messages = [
            {"role": "system", "content": "You are an expert test result analyst. Analyze carefully and respond only with valid JSON."},
            {"role": "user", "content": prompt}
        ]
        
        return self._make_request_with_fallback(messages, "validation", max_tokens=512)

    def translate_text(self, text: str, target_language: str, 
                      source_language: str = "auto") -> Dict[str, Any]:
        """Translate text between languages.
        
        Args:
            text: Text to translate
            target_language: Target language ('en' or 'es')
            source_language: Source language ('en', 'es', or 'auto')
            
        Returns:
            Dict with translated text and metadata
        """
        lang_names = {"en": "English", "es": "Spanish"}
        target_name = lang_names.get(target_language, target_language)
        
        prompt = f"""Translate the following text to {target_name}:

{text}

Provide only the translation, maintaining the original formatting and technical terms where appropriate.
"""
        
        messages = [
            {"role": "system", "content": f"You are an expert translator specializing in technical content. Translate accurately to {target_name}."},
            {"role": "user", "content": prompt}
        ]
        
        return self._make_request_with_fallback(messages, "translation", max_tokens=1024)

    def enhance_user_story(self, user_story: str, language: str = "en") -> Dict[str, Any]:
        """Enhance user story with additional context and details.
        
        Args:
            user_story: Original user story
            language: Response language ('en' or 'es')
            
        Returns:
            Dict with enhanced user story and metadata
        """
        prompt = f"""Enhance this user story with additional context, acceptance criteria, and edge cases:

Original User Story:
{user_story}

Provide an enhanced version that includes:
1. Clear acceptance criteria
2. Edge cases to consider
3. Technical considerations
4. User experience improvements

Respond in {"Spanish" if language == "es" else "English"}.
"""
        
        messages = [
            {"role": "system", "content": "You are an expert product analyst and QA engineer."},
            {"role": "user", "content": prompt}
        ]
        
        return self._make_request_with_fallback(messages, "enhancement")

    def get_usage_stats(self) -> Dict[str, Any]:
        """Get current usage statistics and rate limits.
        
        Returns:
            Dict with credits, rate limits, and usage info
        """
        try:
            # Get credits
            credits = self.client.credits.get()
            
            # Get rate limits
            rate_limits = self.client.calculate_rate_limits()
            
            return {
                "credits": {
                    "balance": credits.data.credits,
                    "usage": credits.data.usage
                },
                "rate_limits": rate_limits,
                "status": "healthy"
            }
        except Exception as e:
            logger.error(f"Error getting usage stats: {e}")
            return {
                "credits": None,
                "rate_limits": None,
                "status": f"error: {e}"
            }

# Singleton instance
_openrouter_service = None

def get_openrouter_service() -> OpenRouterService:
    """Get singleton OpenRouter service instance."""
    global _openrouter_service
    if _openrouter_service is None:
        _openrouter_service = OpenRouterService()
    return _openrouter_service
