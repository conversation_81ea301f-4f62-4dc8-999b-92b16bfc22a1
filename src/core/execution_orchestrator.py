"""
Execution Orchestrator for QAK Test Execution System

Main orchestration service that coordinates all test execution types
with proper resource management, error handling, and result processing.
"""

from typing import Dict, Any, Optional, List
import asyncio
import logging
import json
import os
from datetime import datetime
from contextlib import asynccontextmanager
from dataclasses import asdict
import uuid

# Import core components
from src.core.configuration_manager import BrowserConfig, get_config_for_test
from src.core.result_transformer import process_result, ResultTransformer
from src.models.standard_result import (
    StandardResult, TestType, ExecutionStatus, 
    create_success_result, create_error_result
)
from src.core.execution_strategies import (
    SmokeTestStrategy,
    FullTestStrategy,
    TestCaseStrategy,
    SuiteStrategy,
    CodegenStrategy,
    ExecutionStrategyFactory,
)
from src.core.browser_pool import BrowserPool, BrowserInstance
from src.core.enhanced_artifact_collector import EnhancedArtifactCollector, get_shared_enhanced_collector
from src.core.performance_monitor import PerformanceMonitor
from src.core.execution_context import ExecutionContext
from src.database.repositories.execution_repository import ExecutionRepository
from src.database.repositories.artifact_repository import ArtifactRepository
from src.database.models.artifact import Artifact

logger = logging.getLogger(__name__)


class ExecutionOrchestrator:
    """
    Main orchestration service for all test executions.
    
    Coordinates browser management, configuration resolution, execution strategies,
    and result processing in a centralized, efficient manner.
    """
    
    def __init__(self, execution_repo: Optional[ExecutionRepository] = None):
        """Initialize the execution orchestrator."""
        self.execution_repo = execution_repo
        self.browser_pool = BrowserPool()
        self.artifact_collector = None  # Will be initialized lazily
        self.performance_monitor = PerformanceMonitor()
        self.result_transformer = ResultTransformer()
        self.strategy_factory = ExecutionStrategyFactory()
        
        # Execution tracking
        self.active_executions: Dict[str, ExecutionContext] = {}
        self.total_executions = 0
        self.successful_executions = 0
        self.failed_executions = 0
        
        # Current execution tracking for artifact management
        self.current_execution_id: Optional[str] = None
        
        logger.info("ExecutionOrchestrator initialized")
    
    async def _ensure_artifact_collector(self):
        """Ensure the artifact collector is initialized with shared instance."""
        if self.artifact_collector is None:
            self.artifact_collector = await get_shared_enhanced_collector()
    
    async def execute(self, context: ExecutionContext) -> StandardResult:
        """
        Main execution entry point for all test types.
        
        Args:
            context: The execution context for the test.
            
        Returns:
            StandardResult: Execution result
        """
        # Ensure artifact collector is initialized
        await self._ensure_artifact_collector()
        
        # Set current execution ID for artifact management
        self.current_execution_id = context.execution_id
        
        # Also set it in the repository for artifact updates
        if self.execution_repo:
            self.execution_repo.current_execution_id = context.execution_id
        
        config = context.config
        
        try:
            # Register active execution and start monitoring
            self.active_executions[context.execution_id] = context
            context.update_status(ExecutionStatus.RUNNING)
            self.performance_monitor.start_execution(context.execution_id, context.test_type.value)
            
            # Acquire browser for execution
            browser_session = await self.browser_pool.acquire_browser(config, context.execution_id)
            if not browser_session:
                raise RuntimeError("Failed to acquire browser session")
            
            try:
                context.set_browser(browser_session)
                context.set_artifact_collector(self.artifact_collector)
                # Pass the orchestrator itself into the context for strategies that need it (e.g., SuiteStrategy)
                context.set_orchestrator(self)

                # Execute based on test type using strategy pattern
                raw_result = await self._execute_by_type(context)
                
                # Collect artifacts (the strategy should have already used the collector)
                # We can retrieve the collected artifact paths if needed
                artifacts = self.artifact_collector.get_artifacts_by_execution(context.execution_id)
                logger.info(f"🗂️  Retrieved {len(artifacts)} artifacts for execution {context.execution_id}")
                
                # Note: Artifacts are only stored in R2, not in MongoDB
                # The artifacts are already uploaded to R2 by the enhanced collector
                
                for i, artifact in enumerate(artifacts):
                    logger.info(f"🗂️  Artifact {i+1}: id={getattr(artifact, 'artifact_id', 'unknown')}, type={getattr(artifact, 'type', 'unknown')}, file_path={getattr(artifact, 'file_path', 'unknown')}")
                context.set_artifacts(artifacts)
                
                # Save artifacts to database
                await self._save_artifacts(artifacts)
                
                # Transform the raw result into a StandardResult
                # The suite strategy returns a pre-formatted dict, others return raw history
                if context.test_type == TestType.SUITE:
                    result_type_for_transformer = "suite_execution"
                else:
                    result_type_for_transformer = "browser_history"

                standard_result = await self.result_transformer.process(
                    raw_result,
                    result_type=result_type_for_transformer,
                    execution_id=context.execution_id,
                    test_type=context.test_type,
                    configuration=config.to_dict(),
                    artifacts=artifacts,
                    **context.metadata  # Pass all metadata to the transformer
                )
                
                # Finalize execution status
                logger.info(f"🔍 ORCHESTRATOR DEBUG: standard_result.status = {standard_result.status}")
                logger.info(f"🔍 ORCHESTRATOR DEBUG: standard_result.status type = {type(standard_result.status)}")
                status_value = ""
                if standard_result.status:
                    if hasattr(standard_result.status, 'value'):
                        status_value = standard_result.status.value
                    else:
                        status_value = str(standard_result.status)
                else:
                    status_value = "unknown"
                self.performance_monitor.end_execution(context.execution_id, status_value)
                # Don't call complete_execution again - the result transformer already set the final status
                logger.info(f"🔍 ORCHESTRATOR DEBUG: Final status: {standard_result.status} (type: {type(standard_result.status)})")

                # Save execution result
                await self._save_execution_result(standard_result)

                # Update metrics
                self.total_executions += 1
                if standard_result.success:
                    self.successful_executions += 1
                    context.update_status(ExecutionStatus.SUCCESS)
                else:
                    self.failed_executions += 1
                    context.update_status(ExecutionStatus.FAILURE)
                
                logger.info(f"Execution {context.execution_id} completed successfully")
                return standard_result
            
            finally:
                # Release the browser back to the pool
                await self.browser_pool.release_browser(browser_session, context.execution_id)
            
        except Exception as e:
            # Handle execution errors
            logger.error(f"Execution {context.execution_id} failed: {e}", exc_info=True)
            context.add_error(str(e))
            context.update_status(ExecutionStatus.ERROR)
            self.performance_monitor.end_execution(context.execution_id, ExecutionStatus.ERROR.value)
            
            self.total_executions += 1
            
            error_result = create_error_result(
                test_type=context.test_type,
                error_message=str(e)
            )
            # Set the execution_id manually since create_error_result doesn't accept it
            error_result.execution_id = context.execution_id
            return error_result
            
        finally:
            # Cleanup active execution
            self.active_executions.pop(context.execution_id, None)
    
    async def _execute_by_type(self, context: ExecutionContext) -> Any:
        """
        Route execution to appropriate strategy based on test type.
        
        Args:
            context: Execution context
            
        Returns:
            Raw execution result
        """
        test_type = context.test_type
        strategy = self.strategy_factory.get_strategy(test_type)
        
        if not strategy:
            raise ValueError(f"Unsupported test type: {test_type}")
            
        logger.info(f"Executing with strategy: {strategy.__class__.__name__}")
        return await strategy.execute(context)
    
    async def _process_execution_result(
        self, 
        context: ExecutionContext, 
        raw_result: Any
    ) -> StandardResult:
        """
        Process raw execution result into StandardResult format.
        
        Args:
            context: Execution context
            raw_result: Raw result from execution
            
        Returns:
            StandardResult: Processed result
        """
        try:
            # Use the result processor to convert raw result
            result = await process_result(
                raw_result=raw_result,
                result_type=f"{context.test_type}_test",
                test_type=context.test_type,
                test_id=context.metadata.get("test_id"),
                suite_id=context.metadata.get("suite_id"),
                project_id=context.metadata.get("project_id"),
                configuration=context.config.__dict__
            )
            
            # Update with context information
            result.execution_id = context.execution_id
            result.started_at = context.started_at
            result.completed_at = context.completed_at or datetime.now()
            
            # Add artifacts and errors from context
            for artifact in context.artifacts:
                result.add_artifact(artifact["type"], artifact["path"])
            
            for error in context.errors:
                result.add_error(error)
            
            # Calculate final metrics
            if context.completed_at:
                result.duration_ms = context.get_duration_ms()
            
            return result
            
        except Exception as e:
            logger.error(f"Failed to process execution result: {e}")
            # Return error result as fallback
            return create_error_result(
                test_type=context.test_type,
                error_message=f"Failed to process execution result: {str(e)}"
            )
    
    async def _cleanup_execution(self, context: ExecutionContext):
        """
        Clean up resources after execution completion.
        
        Args:
            context: Execution context to clean up
        """
        try:
            # Clean up temporary files
            # Will implement when ArtifactCollector is ready
            
            logger.debug(f"Cleaned up execution {context.execution_id}")
            
        except Exception as e:
            logger.warning(f"Error during cleanup of {context.execution_id}: {e}")
    
    async def get_execution_status(self, execution_id: str) -> Optional[Dict[str, Any]]:
        """
        Get current status of an execution.
        
        Args:
            execution_id: ID of execution to check
            
        Returns:
            Dict with execution status or None if not found
        """
        context = self.active_executions.get(execution_id)
        if not context:
            return None
        
        return {
            "execution_id": execution_id,
            "test_type": context.test_type,
            "status": context.status,
            "started_at": context.started_at.isoformat(),
            "duration_ms": context.get_duration_ms(),
            "steps_executed": context.steps_executed,
            "errors": context.errors
        }
    
    async def pause_execution(self, execution_id: str) -> bool:
        """
        Pause a running execution.
        
        Args:
            execution_id: ID of execution to pause
            
        Returns:
            bool: True if paused successfully
        """
        context = self.active_executions.get(execution_id)
        if not context:
            return False
        
        try:
            context.pause()
            logger.info(f"Paused execution {execution_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to pause execution {execution_id}: {e}")
            return False

    async def resume_execution(self, execution_id: str) -> bool:
        """
        Resume a paused execution.
        
        Args:
            execution_id: ID of execution to resume
            
        Returns:
            bool: True if resumed successfully
        """
        context = self.active_executions.get(execution_id)
        if not context:
            return False
        
        try:
            context.resume()
            logger.info(f"Resumed execution {execution_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to resume execution {execution_id}: {e}")
            return False

    async def stop_execution(self, execution_id: str) -> bool:
        """
        Stop a running execution.
        
        Args:
            execution_id: ID of execution to stop
            
        Returns:
            bool: True if stopped successfully
        """
        context = self.active_executions.get(execution_id)
        if not context:
            return False
        
        try:
            context.cancel()
            # The execution loop in the agent will check for the cancelled status
            # and stop gracefully. We don't need to pop it from active_executions here,
            # the main execute loop will do that in its `finally` block.
            logger.info(f"Requested to stop execution {execution_id}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop execution {execution_id}: {e}")
            return False
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get real-time performance metrics."""
        return {
            "total_executions": self.total_executions,
            "successful_executions": self.successful_executions,
            "failed_executions": self.failed_executions,
            "active_executions": len(self.active_executions),
            "browser_pool_stats": self.browser_pool.get_pool_stats() if self.browser_pool else {},
            "artifact_collector_stats": self.artifact_collector.get_collector_stats() if self.artifact_collector else {}
        }

    async def _save_artifacts(self, artifacts: List[Any]):
        """
        Los artifacts ya fueron guardados por el collector.
        Este método solo debe registrar la relación con la ejecución.
        
        Args:
            artifacts: List of artifact objects (already saved by collector).
        """
        if not artifacts:
            return
            
        try:
            # Los artifacts ya están guardados en MongoDB por el collector
            # Solo necesitamos actualizar la relación con execution_id si no la tienen
            artifact_ids = []
            for a in artifacts:
                artifact_id = getattr(a, 'artifact_id', None)
                if artifact_id:
                    artifact_ids.append(artifact_id)
            
            if artifact_ids:
                # Actualizar artifacts existentes con execution_id si no lo tienen
                from src.database.models.artifact import Artifact as ArtifactModel
                result = await ArtifactModel.update_many(
                    {
                        "artifact_id": {"$in": artifact_ids}, 
                        "execution_id": {"$exists": False}
                    },
                    {"$set": {"execution_id": self.current_execution_id}}
                )
                
                if result.modified_count > 0:
                    logger.info(f"Updated {result.modified_count} artifacts with execution_id {self.current_execution_id}")
                else:
                    logger.info(f"All {len(artifact_ids)} artifacts already have execution_id")
                
        except Exception as e:
            logger.error(f"Failed to update artifacts with execution_id: {e}", exc_info=True)

    async def _save_execution_result(self, result: StandardResult):
        """
        Save the final execution result to the database and update the
        corresponding test case history.
        
        Args:
            result: The StandardResult object to save.
        """
        try:
            # 1. Save the execution document
            execution_doc = await self.execution_repo.create_execution(result)
            if not execution_doc:
                raise Exception("Failed to create execution document in database.")
            
            logger.info(f"💾 Saved execution {result.execution_id} to the database.")

            # 2. If it's a test case, update its history
            if result.test_type == TestType.CASE and result.metadata:
                # DEBUGGING: Log all metadata to understand what's actually saved
                logger.info(f"🔍 SAVE DEBUG: StandardResult metadata keys: {list(result.metadata.keys())}")
                logger.info(f"🔍 SAVE DEBUG: StandardResult metadata test_id: {result.metadata.get('test_id', 'NOT_FOUND')}")
                logger.info(f"🔍 SAVE DEBUG: StandardResult metadata test_case_id: {result.metadata.get('test_case_id', 'NOT_FOUND')}")
                logger.info(f"🔍 SAVE DEBUG: StandardResult metadata project_id: {result.metadata.get('project_id', 'NOT_FOUND')}")
                logger.info(f"🔍 SAVE DEBUG: StandardResult metadata suite_id: {result.metadata.get('suite_id', 'NOT_FOUND')}")
                logger.info(f"🔍 SAVE DEBUG: StandardResult execution_id: {result.execution_id}")
                logger.info(f"🔍 SAVE DEBUG: StandardResult test_type: {result.test_type}")
                
                test_id = result.metadata.get("test_id")
                if test_id:
                    update_success = await self.execution_repo.add_execution_to_test_history(
                        test_id,
                        result.execution_id,
                        project_id=result.metadata.get("project_id")
                    )
                    if update_success:
                        logger.info(f"📊 Updated history for test case {test_id} with execution {result.execution_id}.")
                    else:
                        logger.warning(f"Could not find test case {test_id} to update history.")
                else:
                    logger.warning("Execution result saved, but no test_id found in metadata to update history.")
            
        except Exception as e:
            logger.error(
                f"Failed to save execution result for {result.execution_id}: {e}",
                exc_info=True
            )

    async def get_saved_execution_result(self, execution_id: str) -> Optional[StandardResult]:
        """
        Retrieve a saved execution result from MongoDB.
        
        Args:
            execution_id: ID of the execution to retrieve
            
        Returns:
            StandardResult or None if not found
        """
        if not self.execution_repo:
            logger.warning("Execution repository not available, cannot fetch from MongoDB.")
            return None
            
        try:
            execution_doc = await self.execution_repo.get_by_execution_id(execution_id)
            
            if not execution_doc:
                logger.warning(f"Execution not found in MongoDB: {execution_id}")
                return None
            
            # Convert the Beanie document to a StandardResult Pydantic model
            standard_result = execution_doc.to_standard_result()
            
            logger.debug(f"Retrieved execution result for {execution_id} from MongoDB")
            return standard_result
            
        except Exception as e:
            logger.error(f"Failed to retrieve execution result {execution_id} from MongoDB: {e}")
            return None


# Singleton instance for shared use across the application
# We will need to initialize this with the repository
execution_orchestrator: Optional[ExecutionOrchestrator] = None

async def initialize_orchestrator():
    """Initializes the singleton execution orchestrator."""
    global execution_orchestrator
    
    # Initialize repository
    repo = ExecutionRepository()
    
    # Initialize orchestrator with repository
    execution_orchestrator = ExecutionOrchestrator(execution_repo=repo)
    
    # Ensure it uses the shared artifact collector
    await execution_orchestrator._ensure_artifact_collector()
    
    logger.info("Execution orchestrator initialized with ExecutionRepository.")

# Call initialization function at module load time
# In a real app, this might be part of a startup sequence (e.g., FastAPI startup event)
# initialize_orchestrator()


async def execute_test(
    test_type: TestType,
    config: BrowserConfig,
    **execution_params
) -> StandardResult:
    """
    Convenience function to run a test using the singleton orchestrator.
    """
    # Ensure orchestrator is initialized (lazy initialization)
    global execution_orchestrator
    if execution_orchestrator is None:
        await initialize_orchestrator()

    # Create an execution context from the parameters
    execution_id = str(uuid.uuid4())
    
    # Extract config details if available, otherwise use defaults
    config_profile = getattr(config, '_profile', 'balanced')
    environment = getattr(config, '_environment', None)
    config_overrides = getattr(config, '_overrides', {})
    
    context = ExecutionContext(
        execution_id=execution_id,
        test_type=test_type,
        config_profile=config_profile,
        environment=environment,
        config_overrides=config_overrides,
        metadata=execution_params
    )
    
    return await execution_orchestrator.execute(context)


async def get_execution_status(execution_id: str) -> Optional[Dict[str, Any]]:
    """
    Convenience function to get execution status from the singleton orchestrator.
    """
    # Ensure orchestrator is initialized
    global execution_orchestrator
    if execution_orchestrator is None:
        await initialize_orchestrator()

    return await execution_orchestrator.get_execution_status(execution_id)


async def get_orchestrator():
    """
    Get the singleton orchestrator, initializing it if necessary.
    """
    global execution_orchestrator
    if execution_orchestrator is None:
        await initialize_orchestrator()
    return execution_orchestrator