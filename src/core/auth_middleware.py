"""
Authentication Middleware for QAK Multi-Tenant System

Handles JWT token validation, user injection, and route protection.
"""

import logging
from typing import Optional, List
from fastapi import Request, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import J<PERSON><PERSON>esponse
from jose import JWTError

from src.services.auth_service import AuthService
from src.services.user_service import UserService
from src.services.organization_service import OrganizationService

logger = logging.getLogger(__name__)

# Security scheme for FastAPI docs
security = HTTPBearer()

# Public routes that don't require authentication
PUBLIC_ROUTES = [
    "/",
    "/health",
    "/docs",
    "/openapi.json",
    "/redoc",
    "/api/health",
    "/api/llm/health",
    "/api/auth/register",
    "/api/auth/login",
    "/api/auth/refresh",
]

# Routes that start with these prefixes are public
PUBLIC_PREFIXES = [
    "/static/",
    "/api/health",
    "/api/llm/health",
]


class AuthMiddleware(BaseHTTPMiddleware):
    """Middleware for JWT authentication and user injection."""
    
    def __init__(self, app, exclude_paths: Optional[List[str]] = None):
        super().__init__(app)
        self.auth_service = AuthService()
        self.user_service = UserService()
        self.org_service = OrganizationService()
        self.exclude_paths = exclude_paths or PUBLIC_ROUTES
    
    def is_public_route(self, path: str) -> bool:
        """Check if a route is public and doesn't require authentication."""
        # Check exact matches
        if path in self.exclude_paths:
            return True
        
        # Check prefix matches
        for prefix in PUBLIC_PREFIXES:
            if path.startswith(prefix):
                return True
        
        return False
    
    async def dispatch(self, request: Request, call_next):
        """Process the request through authentication middleware."""
        path = request.url.path
        
        # Skip authentication for public routes
        if self.is_public_route(path):
            return await call_next(request)
        
        try:
            # Extract token from Authorization header
            authorization = request.headers.get("Authorization")
            if not authorization:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Authorization header missing"}
                )
            
            # Validate Bearer token format
            if not authorization.startswith("Bearer "):
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Invalid authorization header format"}
                )
            
            token = authorization.split(" ")[1]
            
            # Verify JWT token
            try:
                payload = self.auth_service.verify_token(token)
            except JWTError as e:
                logger.warning(f"Token verification failed: {e}")
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Invalid or expired token"}
                )
            
            # Extract user and organization info
            user_id = payload.get("sub")
            org_id = payload.get("org_id")
            
            if not user_id:
                return JSONResponse(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    content={"detail": "Invalid token payload"}
                )
            
            # Get user information
            try:
                user = await self.user_service.get_user_by_id(user_id)
                if not user:
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={"detail": "User not found"}
                    )
                
                if not user.is_active:
                    return JSONResponse(
                        status_code=status.HTTP_401_UNAUTHORIZED,
                        content={"detail": "User account is deactivated"}
                    )
                
            except Exception as e:
                logger.error(f"Failed to get user {user_id}: {e}")
                return JSONResponse(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    content={"detail": "Authentication service error"}
                )
            
            # Get organization information if org_id is present
            organization = None
            if org_id:
                try:
                    organization = await self.org_service.get_organization(org_id)
                    if not organization:
                        return JSONResponse(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            content={"detail": "Organization not found"}
                        )
                    
                    if not organization.is_active:
                        return JSONResponse(
                            status_code=status.HTTP_401_UNAUTHORIZED,
                            content={"detail": "Organization is deactivated"}
                        )
                    
                    # Verify user is a member of the organization
                    from src.database.repositories.organization_member_repository import OrganizationMemberRepository
                    member_repo = OrganizationMemberRepository()
                    is_member = await member_repo.is_user_member(user_id, org_id)
                    
                    if not is_member:
                        return JSONResponse(
                            status_code=status.HTTP_403_FORBIDDEN,
                            content={"detail": "User is not a member of this organization"}
                        )
                    
                except Exception as e:
                    logger.error(f"Failed to get organization {org_id}: {e}")
                    return JSONResponse(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        content={"detail": "Organization service error"}
                    )
            
            # Inject user and organization into request state
            request.state.user = user
            request.state.organization = organization
            request.state.user_id = user_id
            request.state.org_id = org_id
            request.state.token_payload = payload
            
            logger.debug(f"Authenticated user {user_id} for organization {org_id}")
            
        except Exception as e:
            logger.error(f"Authentication middleware error: {e}")
            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content={"detail": "Authentication middleware error"}
            )
        
        # Continue to the next middleware/endpoint
        return await call_next(request)


# Dependency functions for FastAPI endpoints

async def get_current_user(request: Request):
    """FastAPI dependency to get the current authenticated user."""
    if not hasattr(request.state, 'user') or not request.state.user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.user


async def get_current_organization(request: Request):
    """FastAPI dependency to get the current organization."""
    if not hasattr(request.state, 'organization') or not request.state.organization:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Organization context required"
        )
    return request.state.organization


async def get_current_user_id(request: Request) -> str:
    """FastAPI dependency to get the current user ID."""
    if not hasattr(request.state, 'user_id') or not request.state.user_id:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.user_id


async def get_current_org_id(request: Request) -> str:
    """FastAPI dependency to get the current organization ID."""
    if not hasattr(request.state, 'org_id') or not request.state.org_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Organization context required"
        )
    return request.state.org_id


async def get_token_payload(request: Request) -> dict:
    """FastAPI dependency to get the JWT token payload."""
    if not hasattr(request.state, 'token_payload') or not request.state.token_payload:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Authentication required"
        )
    return request.state.token_payload


# Helper function to extract token from request
def get_token_from_request(request: Request) -> Optional[str]:
    """Extract JWT token from request headers."""
    authorization = request.headers.get("Authorization")
    if authorization and authorization.startswith("Bearer "):
        return authorization.split(" ")[1]
    return None
