"""Servicio central para ejecución de pruebas, independiente de la interfaz."""

import os
from typing import Dict, Any, Optional, List
from datetime import datetime

# Configurar path para browser_use local
import sys
import os
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../libs'))

from src.utilities.project_manager_service import ProjectManagerService
from src.utilities.project_manager import Project, TestSuite, TestCase, GitHubConfig
from src.core.execution_orchestrator import ExecutionOrchestrator
from src.api.v2.execution_routes import TestCaseRequest
from src.models.standard_result import StandardResult

from src.core.test_service_extensions import (
    TestServiceSuitesMixin,
    TestServiceTestCasesMixin,
    TestServiceExecutionMixin
)

from browser_use import Browser, Agent as BrowserAgent, Controller, BrowserSession

class TestService(TestServiceSuitesMixin, TestServiceTestCasesMixin, TestServiceExecutionMixin):
    """Servicio para gestionar la ejecución de pruebas, generación de código y proyectos.

    Esta clase actúa como una capa de servicio que desacopla completamente la lógica
    de negocio de las interfaces de usuario (CLI, API web).
    """

    def __init__(self, api_key: str, language: Optional[str] = None):
        """Inicializa el servicio de pruebas.

        Args:
            api_key: API key para el LLM (Google Gemini)
            language: Idioma para los prompts ('en' o 'es')
        """
        self.api_key = api_key
        self.language = language or os.getenv("PROMPT_LANGUAGE", "en")
        self.project_manager = ProjectManagerService()

    async def save_history_to_project(self, project_id: str, suite_id: str, test_history: Dict[str, Any],
                               name: str, description: str, gherkin: str) -> Dict[str, Any]:
        """Guarda un historial de prueba en un proyecto."""
        # Crear un caso de prueba con la información de la prueba
        test_case = await self.project_manager.create_test_case(
            project_id=project_id,
            suite_id=suite_id,
            name=name,
            description=description,
            gherkin=gherkin
        )

        # Actualizar el caso de prueba con el historial
        if test_case and "history_json_path" in test_history:
            test_case.history_files.append(test_history["history_json_path"])

            # Guardar el proyecto actualizado
            project = await self.project_manager.get_project(project_id)
            if project:
                await self.project_manager.save_project(project)

        return test_case.to_dict()

    def generate_code(self, framework: str, gherkin_scenario: str, test_history: Dict[str, Any]) -> str:
        """Genera código de automatización.

        Args:
            framework: Framework para generar el código (selenium, playwright, etc.)
            gherkin_scenario: Escenario Gherkin
            test_history: Historial de la prueba

        Returns:
            str: Código de automatización generado
        """
        from src.core.prompt_service import PromptService
        import json

        # Using modern PromptService - no direct LLM imports needed

        # Preparar datos para la generación
        history_for_code = {
            "urls": test_history.get("urls", []),
            "action_names": test_history.get("action_names", []),
            "detailed_actions": test_history.get("detailed_actions", []),
            "element_xpaths": test_history.get("element_xpaths", {}),
            "extracted_content": test_history.get("extracted_content", []),
            "errors": test_history.get("errors", []),
            "model_actions": test_history.get("model_actions", []),
            "execution_date": test_history.get("execution_date", datetime.now().strftime("%d/%m/%Y %H:%M:%S")),
            "test_id": test_history.get("test_id", "unknown"),
            "screenshot_paths": test_history.get("screenshot_paths", [])
        }

        # Mapeo de frameworks a IDs de prompts
        framework_prompt_ids = {
            "selenium": "code_gen_selenium_pytest",
            "playwright": "code_gen_playwright",
            "cypress": "code_gen_cypress",
            "robot": "code_gen_robot",
            "java": "code_gen_java_selenium"
        }

        # Verificar que el framework solicitado existe
        if framework not in framework_prompt_ids:
            raise ValueError(f"Framework no soportado: {framework}")

        # Use the new prompt service
        prompt_service = PromptService()

        # Map framework names to prompt IDs
        framework_mapping = {
            "selenium": "selenium-pytest",
            "playwright": "playwright",
            "cypress": "cypress",
            "robot": "robot-framework",
            "java": "java-selenium"
        }

        if framework not in framework_mapping:
            raise ValueError(f"Framework no soportado: {framework}")

        prompt_id = framework_mapping[framework]

        # Generate code using the prompt service
        return prompt_service.generate_automation_code(
            framework=prompt_id,
            gherkin_scenario=gherkin_scenario,
            history=history_for_code,
            language=self.language
        )

    def summarize_test_results(self, test_results: str) -> str:
        """Genera un resumen de los resultados de prueba usando IA.

        Args:
            test_results: Resultados de la prueba a resumir

        Returns:
            str: Resumen generado por IA
        """
        from src.core.prompt_service import PromptService

        try:
            # Use the new prompt service
            prompt_service = PromptService()
            return prompt_service.summarize_test_results(test_results, self.language)

        except Exception as e:
            # En caso de error, devolver un mensaje de error descriptivo
            return f"Error al generar el resumen: {str(e)}"

    # === GESTIÓN DE PROYECTOS ===

    async def create_project(self, name: str, description: str = "", tags: List[str] = None,
                      github_config: Optional[Dict[str, Any]] = None,
                      organization_id: Optional[str] = None,
                      created_by: Optional[str] = None) -> Dict[str, Any]:
        """Crea un nuevo proyecto.

        Args:
            name: Nombre del proyecto
            description: Descripción del proyecto
            tags: Lista de etiquetas
            github_config: Configuración de GitHub
            organization_id: ID de la organización
            created_by: ID del usuario que crea el proyecto

        Returns:
            Dict[str, Any]: Datos del proyecto creado
        """
        # Use ProjectService directly for multi-tenant support
        from src.services.project_service import ProjectService
        project_service = ProjectService()

        result = await project_service.create_project(
            name=name,
            description=description,
            tags=tags or [],
            github_config=github_config,
            organization_id=organization_id,
            created_by=created_by
        )

        if result.success:
            return result.data.to_dict()
        else:
            raise Exception(f"Failed to create project: {result.error_message}")

    async def get_project(self, project_id: str, organization_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Obtiene un proyecto por su ID."""
        from src.services.project_service import ProjectService
        project_service = ProjectService()

        result = await project_service.get_project(project_id)
        if not result.success or not result.data:
            return None

        # Validate organization access if provided
        if organization_id and result.data.organization_id != organization_id:
            return None

        return result.data.to_dict()

    async def get_all_projects(self, organization_id: Optional[str] = None) -> List[Dict[str, Any]]:
        """Obtiene todos los proyectos."""
        from src.services.project_service import ProjectService
        project_service = ProjectService()

        result = await project_service.list_projects(organization_id=organization_id)
        if not result.success:
            return []

        return [project.to_dict() for project in result.data.items]

    async def update_project(self, project_id: str, name: str = None, description: str = None,
                      tags: List[str] = None, github_config: Optional[Dict[str, Any]] = None,
                      organization_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Actualiza un proyecto existente."""
        from src.services.project_service import ProjectService
        project_service = ProjectService()

        # First validate the project exists and belongs to the organization
        if organization_id:
            existing_result = await project_service.get_project(project_id)
            if not existing_result.success or not existing_result.data:
                return None
            if existing_result.data.organization_id != organization_id:
                return None

        result = await project_service.update_project(
            project_id=project_id,
            name=name,
            description=description,
            tags=tags,
            github_config=github_config
        )

        if result.success and result.data:
            return result.data.to_dict()
        return None

    async def delete_project(self, project_id: str, organization_id: Optional[str] = None) -> bool:
        """Elimina un proyecto."""
        from src.services.project_service import ProjectService
        project_service = ProjectService()

        result = await project_service.delete_project(project_id, organization_id=organization_id)
        return result.success

    async def create_github_suite(self, project_id: str) -> Optional[Dict[str, Any]]:
        """Crea una suite de pruebas para análisis de Pull Requests de GitHub."""
        project = await self.project_manager.get_project(project_id)
        if not project:
            return None

        # Crear suite de GitHub si no existe ya
        github_suite = next(
            (suite for suite in project.get_all_test_suites() if "github-pr-analysis" in suite.tags),
            None
        )

        if not github_suite:
            return await self.create_test_suite(
                project_id=project_id,
                name="GitHub PR Analysis",
                description="Tests generated from GitHub Pull Request analysis",
                tags=["github-pr-analysis", "automated"]
            )
        
        return github_suite.to_dict()

    # ==========================================
    # COMPATIBILITY METHODS FOR CLI INTERFACE
    # ==========================================
    # These methods maintain CLI compatibility while using the new ExecutionOrchestrator
    
    def create_gherkin_scenario(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None) -> str:
        """Crea un escenario Gherkin a partir de instrucciones.
        
        Esta función mantiene compatibilidad con CLI mientras usa el sistema V2.
        Nota: Esta es una función sincrónica que internamente maneja la conversión async.

        Args:
            instructions: Instrucciones para la prueba
            url: URL para la prueba (opcional)
            user_story: Historia de usuario (opcional)

        Returns:
            str: Escenario Gherkin generado
        """
        import asyncio
        
        async def _create_gherkin_async():
            # Para generar Gherkin, usamos las instrucciones como base
            # y creamos un escenario básico que puede ser usado por ExecutionOrchestrator
            gherkin_template = f"""
Feature: Test automatizado
  Como usuario
  Quiero ejecutar una prueba
  Para verificar el funcionamiento del sistema

  Scenario: Ejecución de prueba
    Given que estoy en la URL "{url or 'https://example.com'}"
    When ejecuto las siguientes instrucciones: "{instructions}"
    Then la prueba debe completarse exitosamente
"""
            return gherkin_template.strip()
        
        # Ejecutar la función async en un loop de eventos
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(_create_gherkin_async())
        except RuntimeError:
            # Si no hay loop activo, crear uno nuevo
            return asyncio.run(_create_gherkin_async())

    def run_smoke_test(self, instructions: str, url: Optional[str] = None, user_story: Optional[str] = None,
                       config_id: Optional[str] = None, configuration: Optional[Dict[str, Any]] = None,
                       browser_session = None) -> Dict[str, Any]:
        """Ejecuta un smoke test usando ExecutionOrchestrator.
        
        Esta función mantiene compatibilidad con CLI mientras usa el sistema V2.
        Nota: Esta es una función sincrónica que internamente maneja la conversión async.

        Args:
            instructions: Instrucciones para el test
            url: URL para la prueba (opcional)
            user_story: Historia de usuario (opcional)
            config_id: ID de configuración predefinida o personalizada a usar (opcional)
            configuration: Configuración específica para la ejecución (opcional)
            browser_session: Sesión de navegador para reutilizar (opcional)

        Returns:
            Dict[str, Any]: Resultado de la ejecución en formato legacy para compatibilidad CLI
        """
        import asyncio
        
        async def _run_smoke_test_async():
            try:
                # Usar ExecutionOrchestrator para ejecutar el test
                orchestrator = ExecutionOrchestrator()
                
                # Crear un escenario Gherkin básico a partir de las instrucciones
                gherkin_scenario = self.create_gherkin_scenario(instructions, url, user_story)
                
                # Crear request compatible con V2 API
                request = TestCaseRequest(
                    test_case_id=f"cli_smoke_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                    gherkin_scenario=gherkin_scenario,
                    url=url or "",
                    test_instructions=instructions,
                    metadata={
                        "user_story": user_story,
                        "config_id": config_id,
                        "configuration": configuration,
                        "browser_session": browser_session,
                        "source": "cli_smoke_test"
                    }
                )
                
                # Ejecutar usando el orquestador
                execution_id = await orchestrator.submit_execution_request(request)
                
                # Esperar a que complete la ejecución
                execution = await orchestrator.get_execution_by_id(execution_id)
                standard_result: StandardResult = execution.result
                
                # Convertir StandardResult al formato legacy para compatibilidad CLI
                result = {
                    "success": standard_result.success,
                    "error": standard_result.error_message if not standard_result.success else None,
                    "test_id": execution_id,
                    "history": standard_result.to_dict(),  # Incluir toda la data para CLI
                    "screenshot_paths": [a.relative_path for a in standard_result.artifacts if a.artifact_type == "screenshot"],
                    "history_path": f"artifacts/{execution_id}/execution_result.json"  # Path where CLI expects the result
                }
                
                return result
                
            except Exception as e:
                return {
                    "success": False,
                    "error": str(e),
                    "test_id": None,
                    "history": None,
                    "screenshot_paths": [],
                    "history_path": None
                }
        
        # Ejecutar la función async en un loop de eventos
        try:
            loop = asyncio.get_event_loop()
            return loop.run_until_complete(_run_smoke_test_async())
        except RuntimeError:
            # Si no hay loop activo, crear uno nuevo
            return asyncio.run(_run_smoke_test_async())

    # ==========================================
    # END COMPATIBILITY METHODS
    # ==========================================