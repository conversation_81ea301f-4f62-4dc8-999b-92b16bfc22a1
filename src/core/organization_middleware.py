"""
Organization Middleware for QAK Multi-Tenant System

Handles multi-tenant data isolation and automatic organization filtering.
"""

import logging
from typing import Optional, Dict, Any
from fastapi import Request, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware

from src.database.models.role import RoleName
from src.core.role_middleware import role_checker

logger = logging.getLogger(__name__)


class OrganizationMiddleware(BaseHTTPMiddleware):
    """Middleware for multi-tenant data isolation."""
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next):
        """Process the request through organization middleware."""
        # This middleware runs after AuthMiddleware, so user and org should be available
        
        # Skip if no authentication context (public routes)
        if not hasattr(request.state, 'user_id') or not request.state.user_id:
            return await call_next(request)
        
        try:
            # Add organization context helpers to request state
            request.state.is_system_admin = await self._is_system_admin(request.state.user_id)
            request.state.can_access_all_orgs = request.state.is_system_admin
            
            # Add organization filtering helper
            request.state.get_org_filter = lambda: self._get_organization_filter(request)
            
            logger.debug(f"Organization context set for user {request.state.user_id}")
            
        except Exception as e:
            logger.error(f"Organization middleware error: {e}")
            # Don't fail the request, just log the error
        
        return await call_next(request)
    
    async def _is_system_admin(self, user_id: str) -> bool:
        """Check if user is a system administrator."""
        try:
            return await role_checker.is_system_admin(user_id)
        except Exception as e:
            logger.error(f"Failed to check system admin status: {e}")
            return False
    
    def _get_organization_filter(self, request: Request) -> Dict[str, Any]:
        """Get organization filter for database queries."""
        # If user is system admin, they can access all organizations
        if getattr(request.state, 'is_system_admin', False):
            return {}  # No filter - access all organizations
        
        # Regular users can only access their organization
        org_id = getattr(request.state, 'org_id', None)
        if org_id:
            return {"organization_id": org_id}
        
        # If no organization context, return empty filter (will likely result in no data)
        return {"organization_id": None}


# Helper functions for organization-aware database operations

def get_organization_filter(request: Request) -> Dict[str, Any]:
    """Get organization filter for database queries."""
    if hasattr(request.state, 'get_org_filter'):
        return request.state.get_org_filter()
    
    # Fallback if middleware didn't run
    org_id = getattr(request.state, 'org_id', None)
    if org_id:
        return {"organization_id": org_id}
    
    return {"organization_id": None}


def can_access_all_organizations(request: Request) -> bool:
    """Check if current user can access all organizations."""
    return getattr(request.state, 'can_access_all_orgs', False)


def is_system_admin(request: Request) -> bool:
    """Check if current user is a system administrator."""
    return getattr(request.state, 'is_system_admin', False)


async def validate_organization_access(request: Request, resource_org_id: str) -> bool:
    """Validate that user can access a resource from a specific organization."""
    # System admins can access all organizations
    if is_system_admin(request):
        return True
    
    # Regular users can only access their organization
    user_org_id = getattr(request.state, 'org_id', None)
    if not user_org_id:
        return False
    
    return user_org_id == resource_org_id


async def ensure_organization_access(request: Request, resource_org_id: str):
    """Ensure user can access a resource from a specific organization, raise exception if not."""
    has_access = await validate_organization_access(request, resource_org_id)
    if not has_access:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied: resource belongs to a different organization"
        )


# Database query helpers for multi-tenant operations

class OrganizationQueryHelper:
    """Helper class for organization-aware database queries."""
    
    @staticmethod
    def add_org_filter(query_filter: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """Add organization filter to a database query."""
        org_filter = get_organization_filter(request)
        return {**query_filter, **org_filter}
    
    @staticmethod
    def ensure_org_context(data: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """Ensure data has organization context for creation/update operations."""
        # Don't override if organization_id is already set
        if "organization_id" in data:
            return data
        
        # Add organization_id from request context
        org_id = getattr(request.state, 'org_id', None)
        if org_id:
            data["organization_id"] = org_id
        
        return data
    
    @staticmethod
    def ensure_user_context(data: Dict[str, Any], request: Request, field_name: str = "created_by") -> Dict[str, Any]:
        """Ensure data has user context for creation operations."""
        # Don't override if field is already set
        if field_name in data:
            return data
        
        # Add user_id from request context
        user_id = getattr(request.state, 'user_id', None)
        if user_id:
            data[field_name] = user_id
        
        return data
    
    @staticmethod
    def prepare_create_data(data: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """Prepare data for creation with organization and user context."""
        data = OrganizationQueryHelper.ensure_org_context(data, request)
        data = OrganizationQueryHelper.ensure_user_context(data, request)
        return data
    
    @staticmethod
    def prepare_execution_data(data: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """Prepare execution data with organization and user context."""
        data = OrganizationQueryHelper.ensure_org_context(data, request)
        data = OrganizationQueryHelper.ensure_user_context(data, request, "executed_by")
        return data
    
    @staticmethod
    def prepare_artifact_data(data: Dict[str, Any], request: Request) -> Dict[str, Any]:
        """Prepare artifact data with organization and user context."""
        data = OrganizationQueryHelper.ensure_org_context(data, request)
        data = OrganizationQueryHelper.ensure_user_context(data, request, "uploaded_by")
        return data


# Convenience instance
org_query_helper = OrganizationQueryHelper()


# FastAPI dependencies for organization context

async def get_org_filter(request: Request) -> Dict[str, Any]:
    """FastAPI dependency to get organization filter."""
    return get_organization_filter(request)


async def require_org_access(request: Request):
    """FastAPI dependency to require organization access."""
    if not hasattr(request.state, 'org_id') or not request.state.org_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Organization context required"
        )
    return True


async def get_org_context(request: Request) -> Dict[str, Any]:
    """FastAPI dependency to get organization context information."""
    return {
        "org_id": getattr(request.state, 'org_id', None),
        "user_id": getattr(request.state, 'user_id', None),
        "is_system_admin": getattr(request.state, 'is_system_admin', False),
        "can_access_all_orgs": getattr(request.state, 'can_access_all_orgs', False),
        "org_filter": get_organization_filter(request)
    }
