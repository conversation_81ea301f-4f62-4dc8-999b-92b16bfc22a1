"""
Role-based Authorization Middleware for QAK Multi-Tenant System

Handles role-based access control and permission checking.
"""

import logging
from typing import List, Optional, Callable
from functools import wraps
from fastapi import Request, HTTPException, status, Depends

from src.database.models.role import RoleName, Permission
from src.services.organization_service import OrganizationService
from src.core.auth_middleware import get_current_user, get_current_user_id, get_current_org_id

logger = logging.getLogger(__name__)


class RoleChecker:
    """Helper class for role and permission checking."""
    
    def __init__(self):
        self.org_service = OrganizationService()
    
    async def check_user_role(self, user_id: str, org_id: str, required_role: RoleName) -> bool:
        """Check if user has the required role in the organization."""
        try:
            from src.database.repositories.organization_member_repository import OrganizationMemberRepository
            from src.database.models.role import Role
            
            member_repo = OrganizationMemberRepository()
            
            # Get user's role in organization
            role_id = await member_repo.get_user_role_in_org(user_id, org_id)
            if not role_id:
                return False
            
            # Get role details
            role = await Role.find_one(Role.role_id == role_id)
            if not role:
                return False
            
            # Check if user has the required role or higher
            role_hierarchy = {
                RoleName.USER: 1,
                RoleName.ORG_ADMIN: 2,
                RoleName.ADMIN: 3
            }
            
            user_role_level = role_hierarchy.get(role.name, 0)
            required_role_level = role_hierarchy.get(required_role, 0)
            
            return user_role_level >= required_role_level
            
        except Exception as e:
            logger.error(f"Failed to check user role: {e}")
            return False
    
    async def check_user_permission(self, user_id: str, org_id: str, required_permission: Permission) -> bool:
        """Check if user has the required permission in the organization."""
        try:
            return await self.org_service.user_has_permission(user_id, org_id, required_permission.value)
        except Exception as e:
            logger.error(f"Failed to check user permission: {e}")
            return False
    
    async def is_system_admin(self, user_id: str) -> bool:
        """Check if user is a system administrator."""
        try:
            from src.database.repositories.organization_member_repository import OrganizationMemberRepository
            from src.database.models.role import Role
            
            member_repo = OrganizationMemberRepository()
            
            # Get all user's memberships
            memberships = await member_repo.get_user_organizations(user_id)
            
            for membership in memberships:
                role = await Role.find_one(Role.role_id == membership.role_id)
                if role and role.name == RoleName.ADMIN:
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to check system admin status: {e}")
            return False


# Global role checker instance
role_checker = RoleChecker()


# Dependency functions for role-based access control

def require_role(required_role: RoleName):
    """FastAPI dependency factory to require a specific role."""
    async def role_dependency(
        request: Request,
        user_id: str = Depends(get_current_user_id),
        org_id: str = Depends(get_current_org_id)
    ):
        has_role = await role_checker.check_user_role(user_id, org_id, required_role)
        if not has_role:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Required role: {required_role.value}"
            )
        return True
    
    return role_dependency


def require_permission(required_permission: Permission):
    """FastAPI dependency factory to require a specific permission."""
    async def permission_dependency(
        request: Request,
        user_id: str = Depends(get_current_user_id),
        org_id: str = Depends(get_current_org_id)
    ):
        has_permission = await role_checker.check_user_permission(user_id, org_id, required_permission)
        if not has_permission:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"Required permission: {required_permission.value}"
            )
        return True
    
    return permission_dependency


def require_org_admin():
    """FastAPI dependency to require organization admin role."""
    return require_role(RoleName.ORG_ADMIN)


def require_system_admin():
    """FastAPI dependency to require system admin role."""
    async def admin_dependency(
        request: Request,
        user_id: str = Depends(get_current_user_id)
    ):
        is_admin = await role_checker.is_system_admin(user_id)
        if not is_admin:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="System administrator access required"
            )
        return True
    
    return admin_dependency


def require_read_permission():
    """FastAPI dependency to require read permission."""
    return require_permission(Permission.READ)


def require_write_permission():
    """FastAPI dependency to require write permission."""
    return require_permission(Permission.WRITE)


def require_delete_permission():
    """FastAPI dependency to require delete permission."""
    return require_permission(Permission.DELETE)


def require_admin_permission():
    """FastAPI dependency to require admin permission."""
    return require_permission(Permission.ADMIN)


# Decorator functions for endpoint protection

def role_required(required_role: RoleName):
    """Decorator to require a specific role for an endpoint."""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request from args/kwargs
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Request object not found"
                )
            
            # Check authentication
            if not hasattr(request.state, 'user_id') or not request.state.user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not hasattr(request.state, 'org_id') or not request.state.org_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Organization context required"
                )
            
            # Check role
            has_role = await role_checker.check_user_role(
                request.state.user_id, 
                request.state.org_id, 
                required_role
            )
            
            if not has_role:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Required role: {required_role.value}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


def permission_required(required_permission: Permission):
    """Decorator to require a specific permission for an endpoint."""
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # Extract request from args/kwargs
            request = None
            for arg in args:
                if isinstance(arg, Request):
                    request = arg
                    break
            
            if not request:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Request object not found"
                )
            
            # Check authentication
            if not hasattr(request.state, 'user_id') or not request.state.user_id:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Authentication required"
                )
            
            if not hasattr(request.state, 'org_id') or not request.state.org_id:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Organization context required"
                )
            
            # Check permission
            has_permission = await role_checker.check_user_permission(
                request.state.user_id, 
                request.state.org_id, 
                required_permission
            )
            
            if not has_permission:
                raise HTTPException(
                    status_code=status.HTTP_403_FORBIDDEN,
                    detail=f"Required permission: {required_permission.value}"
                )
            
            return await func(*args, **kwargs)
        
        return wrapper
    return decorator


# Convenience decorators
org_admin_required = role_required(RoleName.ORG_ADMIN)
read_permission_required = permission_required(Permission.READ)
write_permission_required = permission_required(Permission.WRITE)
delete_permission_required = permission_required(Permission.DELETE)
admin_permission_required = permission_required(Permission.ADMIN)
