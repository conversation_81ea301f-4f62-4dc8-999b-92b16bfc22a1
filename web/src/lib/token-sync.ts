/**
 * Token Synchronization Utilities
 * 
 * Handles synchronization between localStorage and cookies for SSR compatibility.
 */

import Cookies from 'js-cookie';

const TOKEN_KEYS = {
  ACCESS_TOKEN: 'qak_access_token',
  REFRESH_TOKEN: 'qak_refresh_token',
  EXPIRES_AT: 'qak_token_expires_at',
} as const;

/**
 * Set authentication tokens in both localStorage and cookies
 */
export function setAuthTokens(
  accessToken: string,
  refreshToken: string,
  expiresIn: number
): void {
  const expiresAt = Date.now() + (expiresIn * 1000);
  const expiresDate = new Date(expiresAt);

  // Set in localStorage for client-side access
  if (typeof window !== 'undefined') {
    localStorage.setItem(TOKEN_KEYS.ACCESS_TOKEN, accessToken);
    localStorage.setItem(TOKEN_KEYS.REFRESH_TOKEN, refreshToken);
    localStorage.setItem(TOKEN_KEYS.EXPIRES_AT, expiresAt.toString());
  }

  // Set in cookies for SSR and middleware access
  Cookies.set(TOKEN_KEYS.ACCESS_TOKEN, accessToken, {
    expires: expiresDate,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
  });

  Cookies.set(TOKEN_KEYS.REFRESH_TOKEN, refreshToken, {
    expires: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
  });

  Cookies.set(TOKEN_KEYS.EXPIRES_AT, expiresAt.toString(), {
    expires: expiresDate,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax',
    path: '/',
  });
}

/**
 * Get authentication tokens from localStorage or cookies
 */
export function getAuthTokens(): {
  accessToken: string | null;
  refreshToken: string | null;
  expiresAt: number | null;
} {
  let accessToken: string | null = null;
  let refreshToken: string | null = null;
  let expiresAt: number | null = null;

  // Try localStorage first (client-side)
  if (typeof window !== 'undefined') {
    accessToken = localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
    refreshToken = localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
    const expiresAtStr = localStorage.getItem(TOKEN_KEYS.EXPIRES_AT);
    expiresAt = expiresAtStr ? parseInt(expiresAtStr) : null;
  }

  // Fallback to cookies (SSR)
  if (!accessToken) {
    accessToken = Cookies.get(TOKEN_KEYS.ACCESS_TOKEN) || null;
    refreshToken = Cookies.get(TOKEN_KEYS.REFRESH_TOKEN) || null;
    const expiresAtStr = Cookies.get(TOKEN_KEYS.EXPIRES_AT);
    expiresAt = expiresAtStr ? parseInt(expiresAtStr) : null;
  }

  return { accessToken, refreshToken, expiresAt };
}

/**
 * Clear authentication tokens from both localStorage and cookies
 */
export function clearAuthTokens(): void {
  // Clear from localStorage
  if (typeof window !== 'undefined') {
    localStorage.removeItem(TOKEN_KEYS.ACCESS_TOKEN);
    localStorage.removeItem(TOKEN_KEYS.REFRESH_TOKEN);
    localStorage.removeItem(TOKEN_KEYS.EXPIRES_AT);
  }

  // Clear from cookies
  Cookies.remove(TOKEN_KEYS.ACCESS_TOKEN, { path: '/' });
  Cookies.remove(TOKEN_KEYS.REFRESH_TOKEN, { path: '/' });
  Cookies.remove(TOKEN_KEYS.EXPIRES_AT, { path: '/' });
}

/**
 * Check if the current token is expired
 */
export function isTokenExpired(): boolean {
  const { expiresAt } = getAuthTokens();
  
  if (!expiresAt) {
    return true;
  }

  return Date.now() >= expiresAt;
}

/**
 * Check if user is authenticated (has valid token)
 */
export function isAuthenticated(): boolean {
  const { accessToken } = getAuthTokens();
  return !!accessToken && !isTokenExpired();
}

/**
 * Sync tokens from localStorage to cookies (useful after login)
 */
export function syncTokensToCookies(): void {
  if (typeof window === 'undefined') return;

  const accessToken = localStorage.getItem(TOKEN_KEYS.ACCESS_TOKEN);
  const refreshToken = localStorage.getItem(TOKEN_KEYS.REFRESH_TOKEN);
  const expiresAtStr = localStorage.getItem(TOKEN_KEYS.EXPIRES_AT);

  if (accessToken && refreshToken && expiresAtStr) {
    const expiresAt = parseInt(expiresAtStr);
    const expiresIn = Math.max(0, (expiresAt - Date.now()) / 1000);
    
    if (expiresIn > 0) {
      setAuthTokens(accessToken, refreshToken, expiresIn);
    }
  }
}

/**
 * Initialize token synchronization on app start
 */
export function initializeTokenSync(): void {
  if (typeof window === 'undefined') return;

  // Sync tokens to cookies on page load
  syncTokensToCookies();

  // Listen for storage changes to sync across tabs
  window.addEventListener('storage', (event) => {
    if (event.key === TOKEN_KEYS.ACCESS_TOKEN) {
      if (event.newValue) {
        syncTokensToCookies();
      } else {
        clearAuthTokens();
      }
    }
  });
}
