// API General
export interface ApiResponse<T> {
  success: boolean;
  count?: number;
  items?: T[];
  error?: string;
  details?: string;
}

export interface ApiErrorResponse {
  success: boolean;
  error: string;
  details?: string;
}

// API Health
export interface ApiHealth {
  status: string;
  version: string;
  timestamp: string;
  api_key_configured: boolean;
}

// Project
export interface GitHubConfig {
  enabled: boolean;    // si está habilitada la integración con GitHub
  repo?: string;       // formato: "owner/repo"
  token?: string;      // GitHub Personal Access Token
  suite_id?: string;   // ID de la suite automática de GitHub
}

export interface TestSuite {
  suite_id: string;
  project_id: string;
  name: string;
  description: string;
  type: 'manual' | 'github' | 'automated';
  tags: string[];
  created_at: string;
  updated_at: string;
  execution_times: number;
  test_cases?: Record<string, any> | TestCase[];
}

export interface Project {
  project_id: string;
  name: string;
  description: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  test_suites: Record<string, TestSuite>;
  github_config?: GitHubConfig;  // Configuración opcional de GitHub
}

export type ProjectCreateInput = Omit<Project, 'project_id' | 'created_at' | 'updated_at' | 'test_suites'>;
export type ProjectUpdateInput = Partial<Omit<Project, 'project_id' | 'created_at' | 'updated_at' | 'test_suites'>>;

// Test Suite
export type TestSuiteCreateInput = Omit<TestSuite, 'suite_id' | 'project_id' | 'created_at' | 'updated_at' | 'test_cases'>;
export type TestSuiteUpdateInput = Partial<Omit<TestSuite, 'suite_id' | 'project_id'| 'created_at' | 'updated_at' | 'test_cases'>>;

// Test Case
export type TestCaseStatus = 'Not Executed' | 'Passed' | 'Failed';

export interface TestCase {
  test_id: string;
  suite_id: string;
  project_id: string;
  name: string;
  description: string;
  instrucciones: string;
  historia_de_usuario: string;
  gherkin?: string;
  url: string;
  tags: string[];
  created_at: string;
  updated_at: string;
  history_files: string[];
  status: TestCaseStatus;
  last_execution: string | null;
  code?: string;
  framework?: string; // "selenium|playwright|cypress"
}

export type TestCaseCreateInput = Omit<TestCase, 'test_id' | 'suite_id' | 'project_id' | 'created_at' | 'updated_at' | 'history_files' | 'status' | 'last_execution' | 'code' | 'framework'>;
export type TestCaseUpdateInput = Partial<TestCaseCreateInput>;
export type TestCaseStatusUpdateInput = { status: TestCaseStatus };

// Test Execution and History
export interface TestExecutionStepResult {
  step: number;
  content: string;
  success?: boolean;  // true if step completed successfully, false if failed, undefined if unknown
  status?: string;    // "completed", "error", or other status strings
}

export interface TestExecutionAction {
  step: number;
  type: string;
  details: Record<string, any> | string;
}

export interface TestExecutionElement {
  step: number;
  tag_name: string;
  xpath: string;
  attributes: Record<string, string>;
}

export interface TestExecutionUrl {
  step: number;
  url: string;
  title: string;
}

export interface TestExecutionMetadata {
  start_time: string | null;
  end_time: string | null;
  total_steps: number;
  success: boolean;
}

export interface TestExecutionHistoryData {
  actions: TestExecutionAction[];
  results: TestExecutionStepResult[];
  elements: TestExecutionElement[];
  urls: TestExecutionUrl[];
  errors: any[];
  screenshots: string[];
  metadata: TestExecutionMetadata;
  test_id?: string;
  execution_id?: string;
  history_path?: string;
  generatedGherkin?: string;
  userStory?: string;
}

// NEW: V2 API Standard Result Types - Rich Data Support
export interface StandardTestStep {
  step_number: number;
  action_type: string;
  description: string;
  success: boolean;
  duration_ms?: number;
  error_message?: string;
  screenshot_url?: string;
  element_info?: {
    tag?: string;
    id?: string;
    class?: string;
    text?: string;
    selector?: string;
    coordinate?: [number, number];
    [key: string]: any;
  };
  url?: string;
  metadata?: {
    thinking?: string;
    actions?: Array<{
      type: string;
      url?: string;
      text?: string;
      coordinate?: [number, number];
      selector?: string;
      [key: string]: any;
    }>;
    primary_action?: any;
    state?: {
      url?: string;
      screenshot?: string;
      title?: string;
      [key: string]: any;
    };
    result?: any[];
    [key: string]: any;
  };
}

export interface StandardTestSummary {
  total_steps: number;
  successful_steps: number;
  failed_steps: number;
  total_test_cases: number;
  passed_test_cases: number;
  failed_test_cases: number;
  success_rate: number;
}

export interface StandardTestArtifacts {
  screenshots: string[];
  videos: string[];
  logs: string[];
  generated_code?: string;
  history_file?: string;
  gherkin_scenarios: string[];
}

export enum ExecutionStatus {
  SUCCESS = "success",
  FAILURE = "failure",
  ERROR = "error",
  RUNNING = "running",
  PENDING = "pending",
  PAUSED = "paused",
  CANCELLED = "cancelled",
}
export type TestType = "smoke" | "full" | "case" | "suite" | "codegen";

export interface StandardResult {
  execution_id: string;
  test_type: TestType;
  test_id?: string;
  suite_id?: string;
  project_id?: string;
  status: ExecutionStatus;
  started_at: string;
  completed_at?: string;
  start_time: string;  // Frontend compatibility
  end_time?: string;   // Frontend compatibility
  duration_ms?: number;
  summary: StandardTestSummary;
  steps: StandardTestStep[];
  errors: string[];
  artifacts: StandardTestArtifacts;
  configuration: Record<string, any>;
  success: boolean;
  message?: string;
  error?: string;
  raw_data?: Record<string, any>;
  raw_result?: any[]; // Raw result data for processing
  metadata?: {
    visited_urls?: string[];
    interacted_elements?: Array<{
      tag?: string;
      id?: string;
      class?: string;
      text?: string;
      [key: string]: any;
    }>;
    [key: string]: any;
  };
}

export interface TestCaseExecutionResponse {
  success: boolean;
  test_id: string;
  result?: {
    success: boolean;
    test_id: string;
    history_path: string;
    history: TestExecutionHistoryData;
  };
  error: string | null;
}

export interface SuiteExecutionResultItem {
  test_id: string;
  test_name: string;
  result: {
    success: boolean;
    test_id: string;
    history_path: string;
  };
}

export interface SuiteExecutionResponse {
  success: boolean;
  suite_id: string;
  suite_name: string;
  total_tests: number;
  passed: number;
  failed: number;
  results: SuiteExecutionResultItem[];
  execution_time: string;
}

export interface V2ExecutionResponse {
  success: boolean;
  execution_id: string;
  status: ExecutionStatus;
  message?: string;
  result?: StandardResult;
  error?: string;
}

// AI Tool Types
// Types for AI flows - consolidated here to avoid duplication

// Generate Gherkin types
export type GenerateGherkinInput = {
  instructions: string;
  url?: string;
  userStory?: string;
  language?: string;
};

export type GenerateGherkinOutput = {
  gherkin: string;
};

// Generate Code types
export type GenerateCodeInput = {
  framework: string;
  gherkin_scenario: string;
  test_history?: Record<string, any>;
};

export type GenerateCodeOutput = {
  code: string;
};

// Enhance User Story types
export type EnhanceUserStoryInput = {
  userStory: string;
  language?: string;
};

export type EnhanceUserStoryOutput = {
  enhancedUserStory: string;
};

// Generate Manual Test Cases types
export interface TestCaseObject {
  id: string;
  title: string;
  preconditions: string;
  instrucciones: string | string[];
  expected_results: string;
  priority: string;
  historia_de_usuario: string;
}

export type GenerateManualTestCasesInput = {
  userStory: string;
  language?: string;
};

export type GenerateManualTestCasesOutput = {
  manualTestCases: (string | TestCaseObject)[];
};

// Execute Smoke Test types
export type ExecuteSmokeTestInput = {
  baseUrl: string;
  instructions: string;
  userStory?: string;
  configId?: string;
  configuration?: Record<string, any>;
};

export type ExecuteSmokeTestOutput = TestExecutionHistoryData | StandardResult;

// Summarize Test Results types
export type SummarizeTestResultsInput = {
  testResults: string;
};

export type SummarizeTestResultsOutput = {
  summary: string;
};

// V2 Execution Types
export enum ExecutionTypeEnum {
  SMOKE = "smoke",
  FULL = "full",
  CASE = "case",
  SUITE = "suite",
  CODEGEN = "codegen",
}

export interface BaseExecutionRequest {
  type: ExecutionTypeEnum;
  config_profile?: string;
  environment?: string;
  config_overrides?: Record<string, any>;
  options?: Record<string, any>;
}

export interface SmokeTestRequest extends BaseExecutionRequest {
  type: ExecutionTypeEnum.SMOKE;
  url: string;
  instructions?: string;
}

export interface FullTestRequest extends BaseExecutionRequest {
  type: ExecutionTypeEnum.FULL;
  url: string;
  gherkin_scenarios: string[];
}

export interface TestCaseRequest extends BaseExecutionRequest {
  type: ExecutionTypeEnum.CASE;
  test_id: string;
  project_id?: string;
  suite_id?: string;
  execution_times?: number;
}

export interface SuiteRequest extends BaseExecutionRequest {
  type: ExecutionTypeEnum.SUITE;
  suite_id: string;
  project_id?: string;
  parallel?: boolean;
}

export interface CodegenExecutionV2Request extends BaseExecutionRequest {
  type: ExecutionTypeEnum.CODEGEN;
  session_id: string;
}

export type ExecutionRequest =
  | SmokeTestRequest
  | FullTestRequest
  | TestCaseRequest
  | SuiteRequest
  | CodegenExecutionV2Request;

export interface ExecutionResponse {
  success: boolean;
  execution_id: string;
  status: string; // Corresponds to ExecutionStatus enum in backend
  message?: string;
  result?: any; // Corresponds to StandardResult model in backend
  error?: string;
}

// CodeGen types
export type CodegenTargetLanguage = 'javascript' | 'typescript' | 'python' | 'java' | 'csharp';
export type CodegenStatus = 'starting' | 'running' | 'completed' | 'failed' | 'stopped';
export type CodegenColorScheme = 'light' | 'dark';

export interface PlaywrightCodegenRequest {
  // URL y configuración básica
  url?: string;
  target_language: CodegenTargetLanguage;
  
  // Configuración del navegador
  device?: string;
  viewport_size?: string; // "ancho,alto"
  headless?: boolean;
  force_vnc?: boolean; // Forzar modo VNC para pruebas
  
  // Configuración de entorno
  timezone?: string;
  geolocation?: string; // "lat,lng"
  language?: string;
  color_scheme?: CodegenColorScheme;
  
  // Gestión de estado
  load_storage?: string;
  save_storage?: string;
  
  // Integración con QAK
  project_id?: string;
  test_suite?: string;
  user_story?: string;
}

export interface CodegenSessionInfo {
  session_id: string;
  status: CodegenStatus;
  target_language: CodegenTargetLanguage;
  url?: string;
  
  // Timestamps
  created_at: string;
  updated_at: string;
  completed_at?: string;
  
  // Resultados
  generated_code?: string;
  error_message?: string;
  
  // Metadatos
  artifacts_path?: string;
  command_used?: string;
  project_integration?: Record<string, any>;
  
  // VNC Support (para acceso remoto)
  vnc_info?: VncSessionInfo;
  web_vnc_url?: string;
  vnc_port?: number;
  
  // Execution information
  executions?: CodegenExecutionInfo[];
  last_execution?: CodegenExecutionInfo;
  total_executions?: number;
}

export interface VncSessionInfo {
  session_id: string;
  vnc_port: number;
  novnc_port: number;
  display_number: number;
  vnc_url: string;
  web_vnc_url: string;
  created_at: string;
  completed_at?: string;
  status: 'running' | 'completed' | 'error';
  processes: Record<string, number>;
}

export interface VncDependenciesCheck {
  all_dependencies_available: boolean;
  system: string;
  dependencies: Record<string, {
    available: boolean;
    path?: string;
    error?: string;
  }>;
  installation_commands: Record<string, string[]>;
}

export interface CodegenTestCaseRequest {
  session_id: string;
  test_name: string;
  test_description?: string;
  
  // Integración con QAK
  project_id: string;
  test_suite?: string;
  user_story?: string;
  
  // Configuración de conversión
  framework?: string;
  include_assertions?: boolean;
  add_error_handling?: boolean;
}

export interface CodegenStatsResponse {
  total_sessions: number;
  active_sessions: number;
  completed_sessions: number;
  failed_sessions: number;
  
  // Estadísticas por lenguaje
  sessions_by_language: Record<string, number>;
  
  // Promedios
  avg_session_duration?: number;
  avg_generated_lines?: number;
  
  // Última actividad
  last_session_at?: string;
}

export interface CodegenSessionListResponse {
  total_sessions: number;
  sessions: Array<{
    session_id: string;
    status: CodegenStatus;
    target_language: CodegenTargetLanguage;
    url?: string;
    created_at: string;
    updated_at: string;
  }>;
}

export interface CodegenHealthResponse {
  service_status: string;
  playwright_available: boolean;
  playwright_version?: string;
  active_sessions: number;
  total_sessions: number;
  service_uptime: string;
}

// Codegen History Types
export interface CodegenHistorySession {
  session_id: string;
  status: CodegenStatus;
  target_language: CodegenTargetLanguage;
  url?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  has_generated_code: boolean;
  command_used?: string;
  error_message?: string;
  project_integration?: {
    project_id: string;
    test_suite?: string;
    user_story?: string;
  };
}

export interface CodegenHistoryResponse {
  total_sessions: number;
  sessions: CodegenHistorySession[];
}

export interface CodegenHistorySessionDetailResponse {
  session_id: string;
  status: CodegenStatus;
  target_language: CodegenTargetLanguage;
  url?: string;
  created_at: string;
  updated_at: string;
  completed_at?: string;
  command_used?: string;
  error_message?: string;
  generated_code?: string;
  project_integration?: {
    project_id: string;
    test_suite: string;
    user_story: string;
  };
}

// Codegen Execution Types
export interface CodegenExecutionRequest {
  session_id: string;
  config_id?: string;
  configuration?: Record<string, any>;
}

export interface CodegenExecutionResponse {
  execution_id: string;
  status: string;
  message: string;
}

export interface CodegenExecutionStep {
  step_number: number;
  action: string;
  description: string;
  status: "completed" | "error" | "skipped";
  timestamp?: string;
  details?: {
    error?: string;
    result?: string;
    evaluation?: string;
    memory?: string;
    next_goal?: string;
    error_type?: string;
    error_message?: string;
    step_index?: number;
    [key: string]: any;
  };
}

export interface CodegenExecutionSummary {
  total_steps: number;
  total_duration_seconds: number;
  total_input_tokens: number;
  total_output_tokens: number;
  total_cost: number;
  final_url?: string;
  is_done: boolean;
  is_successful: boolean;
  has_errors: boolean;
}

export interface CodegenExecutionInfo {
  execution_id: string;
  session_id: string;
  status: "starting" | "running" | "completed" | "failed" | "stopped" | "completed_with_failures";
  created_at: string;
  updated_at: string;
  completed_at?: string;
  target_url?: string;
  generated_code: string;
  browser_config: Record<string, any>;
  history: any[]; // Mantener por compatibilidad, aunque ahora usemos 'steps'
  screenshots?: string[];
  summary?: CodegenExecutionSummary;
  steps?: CodegenExecutionStep[];
  final_result?: string;
  error?: string;
}

export interface CodegenExecutionListResponse {
  total_executions: number;
  executions: Array<{
    execution_id: string;
    session_id: string;
    status: string;
    created_at: string;
    updated_at: string;
    target_url?: string;
  }>;
}

// For forms
export type ProjectFormData = ProjectCreateInput;
export type SuiteFormData = TestSuiteCreateInput;
export type TestCaseFormData = TestCaseCreateInput;

// Utility types
export interface NavItem {
  title: string;
  href: string;
  icon: React.ReactNode;
  disabled?: boolean;
  external?: boolean;
  label?: string;
  items?: NavItem[];
}

