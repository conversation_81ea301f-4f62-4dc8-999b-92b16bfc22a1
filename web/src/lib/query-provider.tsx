"use client";

import type { PropsWithChildren } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { ReactQueryDevtools } from '@tanstack/react-query-devtools';
import React from 'react';
import { authApi } from '@/lib/auth-api';
import { clearAuthTokens } from '@/lib/token-sync';

export function Providers({ children }: PropsWithChildren) {
  const [client] = React.useState(() => new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 5 * 60 * 1000, // 5 minutes
        refetchOnWindowFocus: false, // Optional: disable refetch on window focus
        retry: (failureCount, error: any) => {
          // Don't retry on authentication errors
          if (error?.status === 401 || error?.status === 403) {
            return false;
          }
          // Retry up to 3 times for other errors
          return failureCount < 3;
        },
      },
      mutations: {
        retry: (failureCount, error: any) => {
          // Don't retry on authentication errors
          if (error?.status === 401 || error?.status === 403) {
            return false;
          }
          // Retry once for other errors
          return failureCount < 1;
        },
      },
    },
    mutationCache: {
      onError: (error: any) => {
        // Handle authentication errors globally
        if (error?.status === 401) {
          // Clear tokens and redirect to login
          clearAuthTokens();
          window.location.href = '/auth/login';
        }
      },
    },
    queryCache: {
      onError: (error: any) => {
        // Handle authentication errors globally
        if (error?.status === 401) {
          // Try to refresh token first
          authApi.refreshToken()
            .then(() => {
              // Token refreshed successfully, retry the query
              client.invalidateQueries();
            })
            .catch(() => {
              // Refresh failed, clear tokens and redirect
              clearAuthTokens();
              window.location.href = '/auth/login';
            });
        }
      },
    },
  }));

  return (
    <QueryClientProvider client={client}>
      {children}
      <ReactQueryDevtools initialIsOpen={false} />
    </QueryClientProvider>
  );
}
