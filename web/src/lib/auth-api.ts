/**
 * Authentication API Client
 * 
 * Handles all authentication-related API calls to the QAK backend.
 */

import {
  LoginRequest,
  LoginResponse,
  RegisterRequest,
  RegisterResponse,
  RefreshTokenRequest,
  RefreshTokenResponse,
  UserProfile,
  ChangePasswordRequest,
  ApiError
} from '@/types/auth';
import { setAuthTokens, getAuthTokens, clearAuthTokens, isTokenExpired } from '@/lib/token-sync';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8000';

class AuthApiError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public details?: any
  ) {
    super(message);
    this.name = 'AuthApiError';
  }
}

/**
 * Authentication API client class
 */
export class AuthApi {
  private baseUrl: string;

  constructor(baseUrl: string = API_BASE_URL) {
    this.baseUrl = baseUrl;
  }

  /**
   * Make authenticated API request
   */
  private async makeRequest<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseUrl}${endpoint}`;
    
    const defaultHeaders = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    // Add authorization header if token exists
    const token = this.getStoredToken();
    if (token) {
      defaultHeaders['Authorization'] = `Bearer ${token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers: defaultHeaders,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new AuthApiError(
        errorData.detail || `HTTP ${response.status}: ${response.statusText}`,
        response.status,
        errorData
      );
    }

    return response.json();
  }

  /**
   * Get stored access token
   */
  private getStoredToken(): string | null {
    const { accessToken } = getAuthTokens();
    return accessToken;
  }

  /**
   * Store authentication tokens
   */
  private storeTokens(accessToken: string, refreshToken: string, expiresIn: number): void {
    setAuthTokens(accessToken, refreshToken, expiresIn);
  }

  /**
   * Clear stored tokens
   */
  private clearTokens(): void {
    clearAuthTokens();
  }

  /**
   * Check if token is expired
   */
  private isTokenExpired(): boolean {
    return isTokenExpired();
  }

  /**
   * Login user
   */
  async login(credentials: LoginRequest): Promise<LoginResponse> {
    const response = await this.makeRequest<LoginResponse>('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify(credentials),
    });

    // Store tokens
    this.storeTokens(
      response.access_token,
      response.refresh_token,
      response.expires_in
    );

    return response;
  }

  /**
   * Register new user
   */
  async register(userData: RegisterRequest): Promise<RegisterResponse> {
    return this.makeRequest<RegisterResponse>('/api/auth/register', {
      method: 'POST',
      body: JSON.stringify(userData),
    });
  }

  /**
   * Logout user
   */
  async logout(): Promise<void> {
    try {
      await this.makeRequest('/api/auth/logout', {
        method: 'POST',
      });
    } catch (error) {
      // Continue with logout even if API call fails
      console.warn('Logout API call failed:', error);
    } finally {
      this.clearTokens();
    }
  }

  /**
   * Get current user profile
   */
  async getMe(): Promise<UserProfile> {
    return this.makeRequest<UserProfile>('/api/auth/me');
  }

  /**
   * Refresh access token
   */
  async refreshToken(): Promise<RefreshTokenResponse> {
    const { refreshToken } = getAuthTokens();
    if (!refreshToken) {
      throw new AuthApiError('No refresh token available');
    }

    const response = await this.makeRequest<RefreshTokenResponse>('/api/auth/refresh', {
      method: 'POST',
      body: JSON.stringify({ refresh_token: refreshToken }),
    });

    // Update stored access token
    setAuthTokens(response.access_token, refreshToken, response.expires_in);

    return response;
  }

  /**
   * Change user password
   */
  async changePassword(passwordData: ChangePasswordRequest): Promise<void> {
    await this.makeRequest('/api/auth/change-password', {
      method: 'POST',
      body: JSON.stringify(passwordData),
    });
  }

  /**
   * Check if user is authenticated
   */
  isAuthenticated(): boolean {
    const token = this.getStoredToken();
    return token !== null && !this.isTokenExpired();
  }

  /**
   * Get stored tokens
   */
  getStoredTokens(): { accessToken: string | null; refreshToken: string | null } {
    const { accessToken, refreshToken } = getAuthTokens();
    return { accessToken, refreshToken };
  }
}

// Export singleton instance
export const authApi = new AuthApi();

// Export error class
export { AuthApiError };
