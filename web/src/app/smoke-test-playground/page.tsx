"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription, DialogFooter, DialogClose } from "@/components/ui/dialog";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Label } from "@/components/ui/label";

import { useToast } from "@/hooks/use-toast";
import { executeTest, getAllConfigurations, executeSmokeTestV2, executeTestUnified, pauseExecution, resumeExecution, stopExecution, getExecutionById } from "@/lib/api";
import { 
  ExecutionRequest, 
  ExecutionResponse as ExecutionResponseV2, 
  TestExecutionHistoryData,
  ExecutionTypeEnum,
  StandardResult,
  ExecutionStatus,
  V2ExecutionResponse
} from "@/lib/types";
import { UnifiedResultsViewer } from "@/components/execution/UnifiedResultsViewer";
import { GherkinHighlighter } from "@/components/execution/GherkinHighlighter";
import { ManualTestCasesTable } from "@/components/execution/ManualTestCasesTable";
import { Flame, Sparkles, Copy, Save } from "lucide-react";
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";
import { copyToClipboard } from "@/lib/clipboard";

const smokeTestPlaygroundSchema = z.object({
  baseUrl: z.string().url("A valid Base URL is required."),
  instructions: z.string().min(20, "Instructions must be at least 20 characters."),
  userStory: z.string().optional(),
  configId: z.string().optional(),
});
type SmokeTestPlaygroundFormData = z.infer<typeof smokeTestPlaygroundSchema>;

// Type guard to check if the result is a StandardResult
const isStandardResult = (result: any): result is StandardResult => {
  return result && typeof result === 'object' && 'execution_id' in result && 'status' in result && 'test_type' in result;
};

// Type guard to check if the result is a V2ExecutionResponse (async mode)
const isV2ExecutionResponse = (result: any): result is V2ExecutionResponse => {
  return result && typeof result === 'object' && 'execution_id' in result && 'status' in result && 'success' in result && 'message' in result;
};

function ExecutionLoadingSkeleton() {
  return (
    <div className="space-y-4 mt-6">
      <Skeleton className="h-10 w-1/3" /> {/* Header */}
      <Skeleton className="h-12 w-full rounded-md" /> {/* Tabs List */}
      <div className="mt-4 p-4 rounded-lg border bg-card">
        <Skeleton className="h-6 w-1/4 mb-4" /> {/* Tab Title */}
        <div className="space-y-4">
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-40 w-full" />
          <Skeleton className="h-32 w-full" />
        </div>
      </div>
    </div>
  );
}


export default function SmokeTestPlaygroundPage() {
  const { toast } = useToast();
  const [executionResult, setExecutionResult] = useState<TestExecutionHistoryData | StandardResult | null>(null);
  const [executionId, setExecutionId] = useState<string | null>(null);
  const [executionStatus, setExecutionStatus] = useState<ExecutionStatus | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [showSaveModal, setShowSaveModal] = useState(false);
  const [formDataForSave, setFormDataForSave] = useState<SmokeTestPlaygroundFormData | null>(null);
  const [enhancedUserStory, setEnhancedUserStory] = useState<string>("");
  const [manualTestCases, setManualTestCases] = useState<string[]>([]);
  const [configurations, setConfigurations] = useState<any[]>([]);
  const [isLoadingConfigurations, setIsLoadingConfigurations] = useState(false);


  const form = useForm<SmokeTestPlaygroundFormData>({
    resolver: zodResolver(smokeTestPlaygroundSchema),
    defaultValues: {
      baseUrl: "",
      instructions: "",
      userStory: "",
      configId: "default",
    },
  });

  // Load configurations on component mount
  useEffect(() => {
    const loadConfigurations = async () => {
      setIsLoadingConfigurations(true);
      try {
        const response = await getAllConfigurations();
        // API returns { predefined: [], custom: [] }, so we need to flatten it
        const allConfigs = [
          ...response.predefined.map((config: any) => ({
            id: config.config_type,
            name: config.name,
            description: config.description,
            settings: config.settings,
            type: 'predefined'
          })),
          ...response.custom.map((config: any) => ({
            id: config.config_id,
            name: config.name,
            description: config.description,
            settings: config.settings,
            type: 'custom'
          }))
        ];
        setConfigurations(allConfigs);
      } catch (err) {
        console.error('Failed to load configurations:', err);
        toast({ 
          title: "Error Loading Configurations", 
          description: "Could not load test configurations. Using default configuration.",
          variant: "destructive" 
        });
      } finally {
        setIsLoadingConfigurations(false);
      }
    };

    loadConfigurations();
  }, [toast]);

  const onExecuteSubmit = async (data: SmokeTestPlaygroundFormData) => {
    setIsLoading(true);
    setError(null);
    setExecutionResult(null);
    setExecutionId(null);
    setExecutionStatus(null);
    setFormDataForSave(data); // Store form data for potential save

    try {
      // Use the new V2 API with rich data extraction - now returns immediately with RUNNING status
      const result = await executeTestUnified({
        type: "smoke",
        url: data.baseUrl,
        instructions: data.instructions,
        config_profile: data.configId === "default" ? "fast" : data.configId,
      });

      console.log("🚀 executeTestUnified immediate result:", result);

      // Check if this is the new async response format (V2ExecutionResponse with RUNNING status)
      if (isV2ExecutionResponse(result) && result.status === ExecutionStatus.RUNNING) {
        // New async mode - execution started in background
        setExecutionId(result.execution_id);
        setExecutionStatus(ExecutionStatus.RUNNING);
        console.log("🎯 Execution started in background:", result.execution_id);
        
        toast({
          title: "🚀 Test execution started!",
          description: "Test is running in the background. You can use the controls to pause, resume, or cancel.",
        });

        // Start polling for execution status
        startPollingExecution(result.execution_id);
        
      } else if (isStandardResult(result) || (result && typeof result === 'object' && 'execution_id' in result)) {
        // Legacy mode - execution completed immediately (StandardResult or similar)
        handleLegacyResult(result);
      } else {
        // Unexpected result format
        console.error("⚠️ Unexpected result format:", result);
        throw new Error("Unexpected response format from server");
      }

      // If there was a user story, create an enhanced version for editing
      if (data.userStory) {
        setEnhancedUserStory(data.userStory);
      }

      // Generate sample manual test cases based on instructions
      const instructionLines = data.instructions.split('\n').filter(line => line.trim());
      const sampleTestCases = instructionLines.map(line => {
        const cleanLine = line.replace(/^\d+\.\s*/, "");
        return cleanLine;
      });
      setManualTestCases(sampleTestCases);

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Unknown error occurred";
      setError(errorMessage);
      console.error("Smoke test execution failed:", error);
      toast({
        title: "❌ Test execution failed",
        description: errorMessage,
        variant: "destructive",
      });
      setIsLoading(false);
    }
  };

  // Helper function to handle legacy execution results (for backward compatibility)
  const handleLegacyResult = (result: any) => {
    setExecutionResult(result);

    // Extract execution_id and status from the result regardless of type
    let extractedExecutionId: string | null = null;
    let extractedStatus: ExecutionStatus | null = null;

    if (isStandardResult(result)) {
      extractedExecutionId = result.execution_id;
      extractedStatus = result.status;
    } else if (result.execution_id) {
      // For results that have execution_id but aren't full StandardResult
      extractedExecutionId = result.execution_id;
      extractedStatus = result.metadata?.success ? ExecutionStatus.SUCCESS : ExecutionStatus.FAILURE;
    } else {
      // For legacy TestExecutionHistoryData without execution_id
      extractedStatus = result.metadata?.success ? ExecutionStatus.SUCCESS : ExecutionStatus.FAILURE;
    }

    // Set the execution state
    if (extractedExecutionId) {
      setExecutionId(extractedExecutionId);
      setExecutionStatus(extractedStatus || ExecutionStatus.SUCCESS);
      console.log("🎯 Execution ID extracted:", extractedExecutionId, "Status:", extractedStatus);
    } else {
      console.warn("⚠️ No execution_id found in result:", result);
      setExecutionStatus(extractedStatus || ExecutionStatus.FAILURE);
    }

    // Show success toast
    const isSuccess = isStandardResult(result) ? result.success : result.metadata?.success !== false;
    toast({
      title: "✅ Test executed successfully!",
      description: `Test completed with ${isSuccess ? 'success' : 'failure'} status.`,
    });

    setIsLoading(false);
  };

  // Function to poll execution status
  const startPollingExecution = (execId: string) => {
    const pollInterval = setInterval(async () => {
      try {
        const statusResponse: V2ExecutionResponse = await getExecutionById(execId);
        console.log("📊 Polling status for", execId, ":", statusResponse.status);
        
        setExecutionStatus(statusResponse.status);
        
        // Check if execution is complete
        if ([ExecutionStatus.SUCCESS, ExecutionStatus.FAILURE, ExecutionStatus.ERROR, ExecutionStatus.CANCELLED].includes(statusResponse.status)) {
          clearInterval(pollInterval);
          setIsLoading(false);
          
          // Set the final result if available
          if (statusResponse.result) {
            setExecutionResult(statusResponse.result);
          }
          
          // Show completion toast
          const isSuccess = statusResponse.status === ExecutionStatus.SUCCESS;
          toast({
            title: isSuccess ? "✅ Test completed successfully!" : "❌ Test execution failed",
            description: `Test finished with ${statusResponse.status} status.`,
            variant: isSuccess ? "default" : "destructive"
          });
        }
        
      } catch (error) {
        console.error("Error polling execution status:", error);
        // Don't stop polling on individual errors, but limit attempts
      }
    }, 2000); // Poll every 2 seconds

    // Stop polling after 10 minutes as a safety measure
    setTimeout(() => {
      clearInterval(pollInterval);
      if (isLoading) {
        setIsLoading(false);
        toast({
          title: "⏰ Polling timeout",
          description: "Stopped checking execution status after 10 minutes.",
          variant: "destructive"
        });
      }
    }, 600000); // 10 minutes
  };

  const handlePause = async () => {
    if (!executionId) return;
    try {
      await pauseExecution(executionId);
      setExecutionStatus(ExecutionStatus.PAUSED);
      toast({ title: "Execution Paused", description: "The test execution has been paused." });
    } catch (error) {
      toast({ title: "Error", description: `Failed to pause execution: ${(error as Error).message}`, variant: "destructive" });
    }
  };

  const handleResume = async () => {
    if (!executionId) return;
    try {
      await resumeExecution(executionId);
      setExecutionStatus(ExecutionStatus.RUNNING);
      toast({ title: "Execution Resumed", description: "The test execution has been resumed." });
    } catch (error) {
      toast({ title: "Error", description: `Failed to resume execution: ${(error as Error).message}`, variant: "destructive" });
    }
  };

  const handleStop = async () => {
    if (!executionId) return;
    try {
      await stopExecution(executionId);
      setExecutionStatus(ExecutionStatus.CANCELLED);
      toast({ title: "Execution Cancelled", description: "The test execution has been cancelled." });
    } catch (error) {
      toast({ title: "Error", description: `Failed to cancel execution: ${(error as Error).message}`, variant: "destructive" });
    }
  };

  const handleCopyToClipboard = async (text: string, fieldName: string) => {
    const success = await copyToClipboard(text);
    
    if (success) {
      toast({ title: "Copied to Clipboard", description: `${fieldName} copied.` });
    } else {
      toast({ 
        title: "Copy Failed", 
        description: `Could not copy ${fieldName}. You may need to copy manually.`, 
        variant: "destructive" 
      });
    }
  };


  return (
    <div>
      <h1 className="page-header flex items-center gap-2"><Flame /> Smoke Test Playground</h1>
      <Card>
        <CardHeader>
          <CardTitle>Configure Smoke Test</CardTitle>
          <CardDescription>Provide the necessary details to simulate a smoke test using AI.</CardDescription>
        </CardHeader>
        <CardContent>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onExecuteSubmit)} className="space-y-6">
              <FormField control={form.control} name="baseUrl" render={({ field }) => (
                <FormItem>
                  <FormLabel>Base URL</FormLabel>
                  <FormControl><Input type="url" placeholder="https://example.com" {...field} /></FormControl>
                  <FormDescription>The main URL of the application or feature to test.</FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="instructions" render={({ field }) => (
                <FormItem>
                  <FormLabel>Instructions / Test Steps</FormLabel>
                  <FormControl><Textarea placeholder="1. Navigate to login page.\n2. Enter '<EMAIL>' in email field.\n3. Enter 'password123' in password field.\n4. Click 'Login' button.\n5. Verify 'Dashboard' text is visible." {...field} className="min-h-[150px]" /></FormControl>
                  <FormDescription>Detailed steps the AI should simulate for the smoke test.</FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="userStory" render={({ field }) => (
                <FormItem>
                  <div className="flex items-center justify-between">
                    <FormLabel>User Story (Optional)</FormLabel>
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      className="h-8"
                      onClick={() => field.value && setEnhancedUserStory(field.value)}
                      disabled={!field.value}
                    >
                      <Sparkles className="mr-2 h-4 w-4" />
                      Enhance with AI
                    </Button>
                  </div>
                  <FormControl><Textarea placeholder="e.g., As a new user, I want to be able to register an account successfully." {...field} className="min-h-[80px]" /></FormControl>
                  <FormDescription>Provide a user story for better context if available.</FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
              
              <FormField control={form.control} name="configId" render={({ field }) => (
                <FormItem>
                  <FormLabel>Test Configuration</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder={isLoadingConfigurations ? "Loading configurations..." : "Select a configuration (optional)"} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="default">Default (smoke test configuration)</SelectItem>
                      {configurations.map((config) => (
                        <SelectItem key={config.id} value={config.id}>
                          <div className="flex flex-col">
                            <span className="font-medium">{config.name}</span>
                            {config.description && (
                              <span className="text-xs text-muted-foreground">{config.description}</span>
                            )}
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    Choose a test configuration to customize timeouts, retry logic, and other test parameters.
                  </FormDescription>
                  {form.watch("configId") && configurations.find(c => c.id === form.watch("configId")) && (
                    <div className="mt-2 p-2 rounded-md bg-muted/50 text-xs">
                      <strong>Configuration Preview:</strong>
                      <pre className="mt-1 text-xs">
                        {JSON.stringify(configurations.find(c => c.id === form.watch("configId"))?.settings || {}, null, 2)}
                      </pre>
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )} />

              <Button type="submit" disabled={isLoading} className="w-full md:w-auto">
                {isLoading ? <Sparkles className="mr-2 h-4 w-4 animate-spin" /> : <Sparkles className="mr-2 h-4 w-4" />}
                Execute Smoke Test
              </Button>
              {isLoading && executionId && (
                <div className="flex gap-2 mt-4 md:mt-0">
                  {executionStatus === ExecutionStatus.RUNNING && (
                    <Button type="button" variant="outline" onClick={handlePause} disabled={!executionId}>
                      Pause
                    </Button>
                  )}
                  {executionStatus === ExecutionStatus.PAUSED && (
                    <Button type="button" variant="outline" onClick={handleResume} disabled={!executionId}>
                      Resume
                    </Button>
                  )}
                  {executionStatus !== ExecutionStatus.SUCCESS && executionStatus !== ExecutionStatus.FAILURE && executionStatus !== ExecutionStatus.CANCELLED && (
                    <Button type="button" variant="destructive" onClick={handleStop} disabled={!executionId}>
                      Cancel
                    </Button>
                  )}
                </div>
              )}
            </form>
          </Form>

          {enhancedUserStory && (
            <div className="mt-6 border rounded-md p-4 bg-muted/20">
              <h3 className="text-lg font-semibold flex items-center mb-3">
                <Sparkles className="h-5 w-5 mr-2 text-primary"/>
                AI Enhanced User Story
              </h3>
              <Textarea
                value={enhancedUserStory}
                onChange={(e) => setEnhancedUserStory(e.target.value)}
                className="min-h-[150px] font-mono text-sm"
              />
            </div>
          )}
        </CardContent>
      </Card>

      {isLoading && <ExecutionLoadingSkeleton />}

      {error && (
        <Alert variant="destructive" className="mt-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Execution Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {executionResult && !isLoading && (
        <div className="mt-8">
          <div className="flex justify-end mb-4">
            <Button onClick={() => setShowSaveModal(true)} variant="outline">
              <Save className="mr-2 h-4 w-4" />
              Prepare to Save Test Case
            </Button>
          </div>
          <UnifiedResultsViewer data={executionResult} />
        </div>
      )}

      {showSaveModal && formDataForSave && executionResult && (
        <Dialog open={showSaveModal} onOpenChange={setShowSaveModal}>
          <DialogContent className="sm:max-w-2xl">
            <DialogHeader>
              <DialogTitle>Save Test Case Details</DialogTitle>
              <DialogDescription>
                Copy the following details to manually create a new Test Case in your desired Project and Suite.
              </DialogDescription>
            </DialogHeader>
            <ScrollArea className="max-h-[60vh] p-1 pr-4">
              <div className="space-y-4 py-4">
                <div>
                  <Label className="font-semibold">Base URL</Label>
                  <div className="mt-1 flex items-center gap-2">
                    <Input readOnly value={formDataForSave.baseUrl} className="font-mono text-sm" />
                    <Button variant="ghost" size="icon" onClick={() => handleCopyToClipboard(formDataForSave.baseUrl, "Base URL")}><Copy className="h-4 w-4"/></Button>
                  </div>
                </div>
                <div>
                  <Label className="font-semibold">Instructions</Label>
                   <div className="mt-1 flex items-start gap-2">
                    <Textarea readOnly value={formDataForSave.instructions} className="font-mono text-sm min-h-[100px]" />
                    <Button variant="ghost" size="icon" onClick={() => handleCopyToClipboard(formDataForSave.instructions, "Instructions")}><Copy className="h-4 w-4"/></Button>
                  </div>
                </div>
                {formDataForSave.userStory && (
                  <div>
                    <Label className="font-semibold">User Story (Original)</Label>
                    <div className="mt-1 flex items-start gap-2">
                      <Textarea readOnly value={formDataForSave.userStory} className="font-mono text-sm min-h-[80px]" />
                      <Button variant="ghost" size="icon" onClick={() => handleCopyToClipboard(formDataForSave.userStory!, "User Story")}><Copy className="h-4 w-4"/></Button>
                    </div>
                  </div>
                )}

                {enhancedUserStory && (
                  <div className="mt-4">
                    <Label className="font-semibold">Enhanced User Story</Label>
                    <div className="mt-1 flex items-start gap-2">
                      <Textarea
                        value={enhancedUserStory}
                        onChange={(e) => setEnhancedUserStory(e.target.value)}
                        className="font-mono text-sm min-h-[120px]"
                      />
                      <Button variant="ghost" size="icon" onClick={() => handleCopyToClipboard(enhancedUserStory, "Enhanced User Story")}><Copy className="h-4 w-4"/></Button>
                    </div>
                  </div>
                )}
                {/* Only show Gherkin for legacy format */}
                {executionResult && 'generatedGherkin' in executionResult && executionResult.generatedGherkin && (
                   <div className="mt-4">
                    <Label className="font-semibold">Generated Gherkin</Label>
                    <div className="mt-1">
                      <GherkinHighlighter gherkinCode={executionResult.generatedGherkin} />
                    </div>
                  </div>
                )}

                <div className="mt-4">
                  <Label className="font-semibold">Manual Test Cases</Label>
                  <div className="mt-1">
                    <ManualTestCasesTable testCases={manualTestCases.length > 0 ? manualTestCases : [
                      "Login to application with valid credentials",
                      "Navigate to dashboard",
                      "Verify all dashboard components load correctly"
                    ]} />
                  </div>
                </div>

                <p className="text-sm text-muted-foreground">
                  Remember to also provide a Name, Description, and Tags when creating the Test Case.
                </p>
              </div>
            </ScrollArea>
            <DialogFooter>
              <DialogClose asChild>
                <Button type="button" variant="secondary">Close</Button>
              </DialogClose>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </div>
  );
}
