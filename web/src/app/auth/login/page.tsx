'use client';

/**
 * Login Page
 * 
 * Provides the login interface for users to authenticate.
 */

import React from 'react';
import { Metadata } from 'next';

import { AuthLayout } from '@/components/auth/AuthLayout';
import { LoginForm } from '@/components/auth/LoginForm';

export default function LoginPage() {
  return (
    <AuthLayout
      title="Sign In to QAK"
      subtitle="Quality Assurance Kit"
      showBackButton={true}
      backButtonText="Back to Home"
      backButtonHref="/"
    >
      <LoginForm />
    </AuthLayout>
  );
}
