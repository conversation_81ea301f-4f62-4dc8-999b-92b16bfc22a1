/**
 * Authentication and User Management Types
 * 
 * Type definitions for the QAK multi-tenant authentication system.
 */

export interface User {
  user_id: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  last_login?: string;
}

export interface Organization {
  org_id: string;
  name: string;
  description?: string;
  plan_type: 'FREE' | 'BASIC' | 'PREMIUM' | 'ENTERPRISE';
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface Role {
  role_id: string;
  name: 'SYSTEM_ADMIN' | 'ORG_ADMIN' | 'USER' | 'VIEWER';
  description: string;
  permissions: Permission[];
}

export interface Permission {
  name: 'READ' | 'WRITE' | 'DELETE' | 'ADMIN';
  description: string;
}

export interface OrganizationMembership {
  organization: Organization;
  role: Role | null;
  joined_at: string;
  is_active: boolean;
}

export interface UserProfile {
  user: User;
  organizations: OrganizationMembership[];
  total_organizations: number;
  active_organizations: number;
}

// Authentication API Types
export interface LoginRequest {
  email: string;
  password: string;
}

export interface LoginResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: UserInfo;
  organization: OrganizationInfo | null;
}

export interface UserInfo {
  user_id: string;
  email: string;
  full_name: string;
  is_active: boolean;
  is_verified: boolean;
  created_at: string;
  last_login?: string;
}

export interface OrganizationInfo {
  org_id: string;
  name: string;
  plan_type: string;
  is_active: boolean;
}

export interface RegisterRequest {
  email: string;
  password: string;
  full_name: string;
  organization_name?: string;
}

export interface RegisterResponse {
  message: string;
  user_id: string;
  organization_id?: string;
}

export interface RefreshTokenRequest {
  refresh_token: string;
}

export interface RefreshTokenResponse {
  access_token: string;
  token_type: string;
  expires_in: number;
}

export interface ChangePasswordRequest {
  current_password: string;
  new_password: string;
}

// Authentication Context Types
export interface AuthState {
  user: User | null;
  organization: Organization | null;
  organizations: Organization[];
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}

export interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => void;
  switchOrganization: (orgId: string) => Promise<void>;
  refreshUser: () => Promise<void>;
  changePassword: (currentPassword: string, newPassword: string) => Promise<void>;
  clearError: () => void;
}

// API Error Types
export interface ApiError {
  detail: string;
  status_code?: number;
}

export interface ValidationError {
  detail: Array<{
    loc: string[];
    msg: string;
    type: string;
  }>;
}

// Token Storage Types
export interface TokenStorage {
  access_token: string;
  refresh_token: string;
  expires_at: number;
}

// Route Protection Types
export interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredPermissions?: Permission['name'][];
  fallback?: React.ReactNode;
}

export interface AuthGuardProps {
  children: React.ReactNode;
  requireAuth?: boolean;
  requiredRole?: Role['name'];
  requiredPermissions?: Permission['name'][];
  fallbackComponent?: React.ComponentType;
  redirectTo?: string;
}
