'use client';

/**
 * Authentication Context
 * 
 * Provides authentication state and methods throughout the application.
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import {
  AuthContextType,
  AuthState,
  User,
  Organization,
  RegisterRequest,
  UserProfile
} from '@/types/auth';
import { authApi, AuthApiError } from '@/lib/auth-api';
import { initializeTokenSync } from '@/lib/token-sync';

// Initial state
const initialState: AuthState = {
  user: null,
  organization: null,
  organizations: [],
  isAuthenticated: false,
  isLoading: true,
  error: null,
};

// Action types
type AuthAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_ERROR'; payload: string | null }
  | { type: 'LOGIN_SUCCESS'; payload: { user: User; organization: Organization | null; organizations: Organization[] } }
  | { type: 'LOGOUT' }
  | { type: 'SET_USER_PROFILE'; payload: UserProfile }
  | { type: 'SWITCH_ORGANIZATION'; payload: Organization };

// Reducer
function authReducer(state: AuthState, action: AuthAction): AuthState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, isLoading: action.payload };
    
    case 'SET_ERROR':
      return { ...state, error: action.payload, isLoading: false };
    
    case 'LOGIN_SUCCESS':
      return {
        ...state,
        user: action.payload.user,
        organization: action.payload.organization,
        organizations: action.payload.organizations,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    
    case 'LOGOUT':
      return {
        ...initialState,
        isLoading: false,
      };
    
    case 'SET_USER_PROFILE':
      return {
        ...state,
        user: action.payload.user,
        organizations: action.payload.organizations.map(org => org.organization),
        organization: state.organization || (action.payload.organizations[0]?.organization || null),
        isAuthenticated: true,
        isLoading: false,
        error: null,
      };
    
    case 'SWITCH_ORGANIZATION':
      return {
        ...state,
        organization: action.payload,
      };
    
    default:
      return state;
  }
}

// Create context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// Provider component
export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Clear error
  const clearError = useCallback(() => {
    dispatch({ type: 'SET_ERROR', payload: null });
  }, []);

  // Login function
  const login = useCallback(async (email: string, password: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      const response = await authApi.login({ email, password });
      
      // Convert response to our types
      const user: User = {
        user_id: response.user.user_id,
        email: response.user.email,
        full_name: response.user.full_name,
        is_active: response.user.is_active,
        is_verified: response.user.is_verified,
        created_at: response.user.created_at,
        last_login: response.user.last_login,
      };

      const organization: Organization | null = response.organization ? {
        org_id: response.organization.org_id,
        name: response.organization.name,
        description: '',
        plan_type: response.organization.plan_type as any,
        is_active: response.organization.is_active,
        created_at: '',
        updated_at: '',
      } : null;

      dispatch({ 
        type: 'LOGIN_SUCCESS', 
        payload: { 
          user, 
          organization, 
          organizations: organization ? [organization] : [] 
        } 
      });

      // Fetch full user profile to get all organizations
      await refreshUser();
      
    } catch (error) {
      const message = error instanceof AuthApiError 
        ? error.message 
        : 'Login failed. Please try again.';
      dispatch({ type: 'SET_ERROR', payload: message });
      throw error;
    }
  }, []);

  // Register function
  const register = useCallback(async (data: RegisterRequest) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: true });
      dispatch({ type: 'SET_ERROR', payload: null });

      await authApi.register(data);
      
      // After successful registration, login the user
      await login(data.email, data.password);
      
    } catch (error) {
      const message = error instanceof AuthApiError 
        ? error.message 
        : 'Registration failed. Please try again.';
      dispatch({ type: 'SET_ERROR', payload: message });
      throw error;
    }
  }, [login]);

  // Logout function
  const logout = useCallback(async () => {
    try {
      await authApi.logout();
    } catch (error) {
      console.warn('Logout API call failed:', error);
    } finally {
      dispatch({ type: 'LOGOUT' });
    }
  }, []);

  // Refresh user data
  const refreshUser = useCallback(async () => {
    try {
      if (!authApi.isAuthenticated()) {
        dispatch({ type: 'LOGOUT' });
        return;
      }

      const profile = await authApi.getMe();
      dispatch({ type: 'SET_USER_PROFILE', payload: profile });
      
    } catch (error) {
      console.error('Failed to refresh user data:', error);
      if (error instanceof AuthApiError && error.statusCode === 401) {
        dispatch({ type: 'LOGOUT' });
      }
    }
  }, []);

  // Switch organization
  const switchOrganization = useCallback(async (orgId: string) => {
    const organization = state.organizations.find(org => org.org_id === orgId);
    if (organization) {
      dispatch({ type: 'SWITCH_ORGANIZATION', payload: organization });
    }
  }, [state.organizations]);

  // Change password
  const changePassword = useCallback(async (currentPassword: string, newPassword: string) => {
    try {
      await authApi.changePassword({
        current_password: currentPassword,
        new_password: newPassword,
      });
    } catch (error) {
      const message = error instanceof AuthApiError 
        ? error.message 
        : 'Failed to change password. Please try again.';
      dispatch({ type: 'SET_ERROR', payload: message });
      throw error;
    }
  }, []);

  // Initialize authentication state on mount
  useEffect(() => {
    const initializeAuth = async () => {
      // Initialize token synchronization
      initializeTokenSync();

      if (authApi.isAuthenticated()) {
        await refreshUser();
      } else {
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeAuth();
  }, [refreshUser]);

  const contextValue: AuthContextType = {
    ...state,
    login,
    register,
    logout,
    switchOrganization,
    refreshUser,
    changePassword,
    clearError,
  };

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// Hook to use auth context
export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
