"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { TestCase, TestCaseCreateInput, TestCaseUpdateInput } from "@/lib/types";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useState } from "react";
import { Badge } from "@/components/ui/badge";
import { XIcon } from "lucide-react";

const testCaseFormSchema = z.object({
  name: z.string().min(2, { message: "Test case name must be at least 2 characters." }),
  description: z.string().optional(),
  instrucciones: z.string().optional(),
  historia_de_usuario: z.string().optional(),
  url: z.string().url({ message: "Please enter a valid URL." }).optional().or(z.literal('')),
  tags: z.array(z.string()).optional(),
});

type TestCaseFormData = z.infer<typeof testCaseFormSchema>;

interface TestCaseFormProps {
  testCase?: TestCase;
  onSubmit: (data: TestCaseCreateInput | TestCaseUpdateInput) => Promise<void>;
  isSubmitting: boolean;
  submitButtonText?: string;
}

export function TestCaseForm({
  testCase,
  onSubmit,
  isSubmitting,
  submitButtonText = "Create Test Case",
}: TestCaseFormProps) {
  const [currentTag, setCurrentTag] = useState("");

  const form = useForm<TestCaseFormData>({
    resolver: zodResolver(testCaseFormSchema),
    defaultValues: {
      name: testCase?.name || "",
      description: testCase?.description || "",
      instrucciones: testCase?.instrucciones || "",
      historia_de_usuario: testCase?.historia_de_usuario || "",
      url: testCase?.url || "",
      tags: testCase?.tags || [],
    },
  });

  const handleAddTag = () => {
    if (currentTag.trim() !== "") {
      const currentTags = form.getValues("tags") || [];
      if(!currentTags.includes(currentTag.trim())) {
        form.setValue("tags", [...currentTags, currentTag.trim()]);
      }
      setCurrentTag("");
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    const currentTags = form.getValues("tags") || [];
    form.setValue("tags", currentTags.filter(tag => tag !== tagToRemove));
  };

  const handleSubmit = async (data: TestCaseFormData) => {
    await onSubmit(data);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>{testCase ? "Edit Test Case" : "Create New Test Case"}</CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Test Case Name</FormLabel>
                  <FormControl>
                    <Input placeholder="Successful Login" {...field} />
                  </FormControl>
                  <FormDescription>
                    A descriptive name for this test case.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Verifies that a user can log in successfully."
                      className="resize-none"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Target URL</FormLabel>
                  <FormControl>
                    <Input placeholder="https://example.com/login" {...field} />
                  </FormControl>
                   <FormDescription>
                    The starting URL for this test case.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="historia_de_usuario"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>User Story</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="As a user, I want to log in..."
                      className="resize-none"
                      rows={4}
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="instrucciones"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Instructions</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="1. Go to the login page..."
                      className="resize-none"
                      rows={6}
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    The manual steps or automation instructions for this test case.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
             <FormField
              control={form.control}
              name="tags"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tags</FormLabel>
                   <div className="flex items-center gap-2">
                    <Input
                      placeholder="Add a tag (e.g., login, critical)"
                      value={currentTag}
                      onChange={(e) => setCurrentTag(e.target.value)}
                      onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                          e.preventDefault();
                          handleAddTag();
                        }
                      }}
                    />
                    <Button type="button" variant="outline" onClick={handleAddTag}>Add Tag</Button>
                  </div>
                  <FormDescription>
                    Keywords to categorize your test case.
                  </FormDescription>
                  {field.value && field.value.length > 0 && (
                    <div className="flex flex-wrap gap-2 mt-2">
                      {field.value.map((tag) => (
                        <Badge key={tag} variant="secondary" className="flex items-center gap-1">
                          {tag}
                           <Button
                            type="button"
                            variant="ghost"
                            size="icon"
                            className="h-4 w-4 text-muted-foreground hover:text-destructive"
                            onClick={() => handleRemoveTag(tag)}
                          >
                            <XIcon size={12} />
                            <span className="sr-only">Remove tag {tag}</span>
                          </Button>
                        </Badge>
                      ))}
                    </div>
                  )}
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={isSubmitting} className="w-full md:w-auto">
              {isSubmitting ? "Submitting..." : submitButtonText}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
} 