'use client';

/**
 * User Profile Component
 * 
 * Displays user information and provides profile management functionality.
 */

import React, { useState } from 'react';
import { User, Settings, Building, LogOut, Shield, Calendar } from 'lucide-react';

import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarInitials } from '@/components/ui/avatar';
import { Separator } from '@/components/ui/separator';

import { useAuth, useUserDisplay, useOrganizationSwitcher } from '@/hooks/use-auth';
import { formatDistanceToNow } from 'date-fns';

interface UserProfileProps {
  showOrganizations?: boolean;
  showActions?: boolean;
  compact?: boolean;
}

export function UserProfile({ 
  showOrganizations = true, 
  showActions = true,
  compact = false 
}: UserProfileProps) {
  const { user, logout } = useAuth();
  const { displayName, initials, organizationName, email, isVerified } = useUserDisplay();
  const { 
    currentOrganization, 
    availableOrganizations, 
    canSwitchOrganization,
    switchToOrganization 
  } = useOrganizationSwitcher();

  const [isLoggingOut, setIsLoggingOut] = useState(false);

  const handleLogout = async () => {
    setIsLoggingOut(true);
    try {
      await logout();
    } catch (error) {
      console.error('Logout failed:', error);
    } finally {
      setIsLoggingOut(false);
    }
  };

  const handleOrganizationSwitch = async (orgId: string) => {
    try {
      await switchToOrganization(orgId);
    } catch (error) {
      console.error('Failed to switch organization:', error);
    }
  };

  if (!user) {
    return null;
  }

  if (compact) {
    return (
      <div className="flex items-center space-x-3">
        <Avatar className="h-8 w-8">
          <AvatarFallback>{initials}</AvatarFallback>
        </Avatar>
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium text-gray-900 truncate">
            {displayName}
          </p>
          <p className="text-xs text-gray-500 truncate">
            {organizationName}
          </p>
        </div>
      </div>
    );
  }

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-4">
        <div className="flex items-center space-x-4">
          <Avatar className="h-16 w-16">
            <AvatarFallback className="text-lg">{initials}</AvatarFallback>
          </Avatar>
          <div className="flex-1">
            <CardTitle className="text-xl">{displayName}</CardTitle>
            <CardDescription className="flex items-center space-x-2">
              <span>{email}</span>
              {isVerified && (
                <Badge variant="secondary" className="text-xs">
                  <Shield className="h-3 w-3 mr-1" />
                  Verified
                </Badge>
              )}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* User Information */}
        <div className="space-y-2">
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">Status</span>
            <Badge variant={user.is_active ? "default" : "secondary"}>
              {user.is_active ? "Active" : "Inactive"}
            </Badge>
          </div>
          
          <div className="flex items-center justify-between text-sm">
            <span className="text-gray-500">Member since</span>
            <span className="flex items-center">
              <Calendar className="h-3 w-3 mr-1" />
              {formatDistanceToNow(new Date(user.created_at), { addSuffix: true })}
            </span>
          </div>

          {user.last_login && (
            <div className="flex items-center justify-between text-sm">
              <span className="text-gray-500">Last login</span>
              <span>{formatDistanceToNow(new Date(user.last_login), { addSuffix: true })}</span>
            </div>
          )}
        </div>

        {/* Current Organization */}
        {showOrganizations && currentOrganization && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium flex items-center">
                <Building className="h-4 w-4 mr-2" />
                Current Organization
              </h4>
              <div className="p-3 bg-gray-50 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">{currentOrganization.name}</p>
                    <p className="text-xs text-gray-500">
                      {currentOrganization.plan_type} Plan
                    </p>
                  </div>
                  <Badge variant={currentOrganization.is_active ? "default" : "secondary"}>
                    {currentOrganization.is_active ? "Active" : "Inactive"}
                  </Badge>
                </div>
              </div>
            </div>
          </>
        )}

        {/* Organization Switcher */}
        {showOrganizations && canSwitchOrganization && (
          <>
            <Separator />
            <div className="space-y-2">
              <h4 className="text-sm font-medium">Switch Organization</h4>
              <div className="space-y-1">
                {availableOrganizations.map((org) => (
                  <button
                    key={org.org_id}
                    onClick={() => handleOrganizationSwitch(org.org_id)}
                    className={`w-full text-left p-2 rounded-md text-sm transition-colors ${
                      org.org_id === currentOrganization?.org_id
                        ? 'bg-blue-50 text-blue-700 border border-blue-200'
                        : 'hover:bg-gray-50'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{org.name}</span>
                      {org.org_id === currentOrganization?.org_id && (
                        <Badge variant="outline" className="text-xs">
                          Current
                        </Badge>
                      )}
                    </div>
                    <p className="text-xs text-gray-500">{org.plan_type} Plan</p>
                  </button>
                ))}
              </div>
            </div>
          </>
        )}

        {/* Actions */}
        {showActions && (
          <>
            <Separator />
            <div className="space-y-2">
              <Button variant="outline" className="w-full justify-start">
                <Settings className="h-4 w-4 mr-2" />
                Account Settings
              </Button>
              
              <Button 
                variant="outline" 
                className="w-full justify-start text-red-600 hover:text-red-700 hover:bg-red-50"
                onClick={handleLogout}
                disabled={isLoggingOut}
              >
                <LogOut className="h-4 w-4 mr-2" />
                {isLoggingOut ? 'Signing out...' : 'Sign Out'}
              </Button>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  );
}
