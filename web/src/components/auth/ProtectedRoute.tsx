'use client';

/**
 * Protected Route Component
 * 
 * Provides route protection based on authentication and permissions.
 */

import React from 'react';
import { useRouter } from 'next/navigation';
import { Loader2, Lock, AlertTriangle } from 'lucide-react';

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { useAuth, usePermissions } from '@/hooks/use-auth';
import { ProtectedRouteProps } from '@/types/auth';

/**
 * Loading component for authentication check
 */
function AuthLoadingScreen() {
  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardContent className="pt-6">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
            <div className="text-center">
              <h3 className="text-lg font-medium">Loading...</h3>
              <p className="text-sm text-gray-500">
                Checking authentication status
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Unauthorized access component
 */
function UnauthorizedScreen({ 
  message = "You don't have permission to access this page.",
  showLoginButton = true 
}: { 
  message?: string; 
  showLoginButton?: boolean; 
}) {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-red-100 flex items-center justify-center">
            <Lock className="h-6 w-6 text-red-600" />
          </div>
          <CardTitle className="text-xl">Access Denied</CardTitle>
          <CardDescription>{message}</CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {showLoginButton && (
            <Button 
              onClick={() => router.push('/auth/login')}
              className="w-full"
            >
              Sign In
            </Button>
          )}
          <Button 
            variant="outline"
            onClick={() => router.back()}
            className="w-full"
          >
            Go Back
          </Button>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Permission denied component
 */
function PermissionDeniedScreen({ requiredPermissions }: { requiredPermissions: string[] }) {
  const router = useRouter();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50">
      <Card className="w-full max-w-md">
        <CardHeader className="text-center">
          <div className="mx-auto mb-4 h-12 w-12 rounded-full bg-yellow-100 flex items-center justify-center">
            <AlertTriangle className="h-6 w-6 text-yellow-600" />
          </div>
          <CardTitle className="text-xl">Insufficient Permissions</CardTitle>
          <CardDescription>
            You need the following permissions to access this page:
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <Alert>
            <AlertDescription>
              <ul className="list-disc list-inside space-y-1">
                {requiredPermissions.map((permission) => (
                  <li key={permission} className="text-sm">
                    {permission}
                  </li>
                ))}
              </ul>
            </AlertDescription>
          </Alert>
          
          <div className="space-y-2">
            <Button 
              variant="outline"
              onClick={() => router.back()}
              className="w-full"
            >
              Go Back
            </Button>
            <p className="text-xs text-center text-gray-500">
              Contact your administrator if you believe this is an error.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

/**
 * Main ProtectedRoute component
 */
export function ProtectedRoute({ 
  children, 
  requiredPermissions = [],
  fallback 
}: ProtectedRouteProps) {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { hasPermission } = usePermissions();

  // Show loading screen while checking authentication
  if (isLoading) {
    return fallback || <AuthLoadingScreen />;
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated || !user) {
    return <UnauthorizedScreen />;
  }

  // Check permissions if required
  if (requiredPermissions.length > 0) {
    const missingPermissions = requiredPermissions.filter(
      permission => !hasPermission(permission as any)
    );

    if (missingPermissions.length > 0) {
      return <PermissionDeniedScreen requiredPermissions={missingPermissions} />;
    }
  }

  // Render children if all checks pass
  return <>{children}</>;
}

/**
 * Higher-order component for protecting pages
 */
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  options: {
    requiredPermissions?: string[];
    fallback?: React.ReactNode;
  } = {}
) {
  const WrappedComponent = (props: P) => {
    return (
      <ProtectedRoute 
        requiredPermissions={options.requiredPermissions}
        fallback={options.fallback}
      >
        <Component {...props} />
      </ProtectedRoute>
    );
  };

  WrappedComponent.displayName = `withAuth(${Component.displayName || Component.name})`;
  
  return WrappedComponent;
}

/**
 * Hook for conditional rendering based on authentication
 */
export function useAuthGuard() {
  const { isAuthenticated, isLoading, user } = useAuth();
  const { hasPermission, hasRole } = usePermissions();

  const canAccess = (requiredPermissions: string[] = []) => {
    if (!isAuthenticated || !user) return false;
    
    return requiredPermissions.every(permission => 
      hasPermission(permission as any)
    );
  };

  const canAccessWithRole = (requiredRole: string) => {
    if (!isAuthenticated || !user) return false;
    
    return hasRole(requiredRole);
  };

  return {
    isAuthenticated,
    isLoading,
    canAccess,
    canAccessWithRole,
    user,
  };
}
