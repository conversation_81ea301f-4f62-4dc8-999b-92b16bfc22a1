'use client';

/**
 * Authenticated Layout Component
 * 
 * Conditionally renders the sidebar and header based on authentication status.
 */

import React from 'react';
import { usePathname } from 'next/navigation';

import { AppSidebar } from '@/components/AppSidebar';
import { AppHeader } from '@/components/AppHeader';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';

import { useAuth } from '@/hooks/use-auth';

interface AuthenticatedLayoutProps {
  children: React.ReactNode;
}

// Routes that should not show the sidebar/header (auth pages, landing page, etc.)
const PUBLIC_ROUTES = [
  '/auth/login',
  '/auth/register',
  '/auth/forgot-password',
  '/auth/reset-password',
  '/auth/verify-email',
  '/', // Landing page
  '/about',
  '/pricing',
  '/contact',
  '/terms',
  '/privacy',
];

export function AuthenticatedLayout({ children }: AuthenticatedLayoutProps) {
  const { isAuthenticated, isLoading } = useAuth();
  const pathname = usePathname();

  // Check if current route is a public route
  const isPublicRoute = PUBLIC_ROUTES.includes(pathname) || pathname.startsWith('/auth/');

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // For public routes or unauthenticated users, render without sidebar
  if (isPublicRoute || !isAuthenticated) {
    return <>{children}</>;
  }

  // For authenticated users on protected routes, render with sidebar
  return (
    <SidebarProvider defaultOpen={true}>
      <AppSidebar />
      <SidebarInset className="flex flex-col">
        <AppHeader />
        <main className="flex-1 p-4 md:p-6 lg:p-8 overflow-auto">
          {children}
        </main>
      </SidebarInset>
    </SidebarProvider>
  );
}
