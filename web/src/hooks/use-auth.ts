/**
 * Authentication Hooks
 * 
 * Custom hooks for authentication-related functionality.
 */

import { useAuth as useAuthContext } from '@/contexts/AuthContext';
import { User, Organization, Permission } from '@/types/auth';

/**
 * Main authentication hook
 */
export function useAuth() {
  return useAuthContext();
}

/**
 * Hook to get current user
 */
export function useUser(): User | null {
  const { user } = useAuthContext();
  return user;
}

/**
 * Hook to get current organization
 */
export function useOrganization(): Organization | null {
  const { organization } = useAuthContext();
  return organization;
}

/**
 * Hook to get all user organizations
 */
export function useOrganizations(): Organization[] {
  const { organizations } = useAuthContext();
  return organizations;
}

/**
 * Hook to check if user is authenticated
 */
export function useIsAuthenticated(): boolean {
  const { isAuthenticated } = useAuthContext();
  return isAuthenticated;
}

/**
 * Hook to check if authentication is loading
 */
export function useAuthLoading(): boolean {
  const { isLoading } = useAuthContext();
  return isLoading;
}

/**
 * Hook to get authentication error
 */
export function useAuthError(): string | null {
  const { error } = useAuthContext();
  return error;
}

/**
 * Hook to check user permissions
 */
export function usePermissions() {
  const { user, organization } = useAuthContext();
  
  const hasPermission = (permission: Permission['name']): boolean => {
    // System admins have all permissions
    if (user?.email === '<EMAIL>') {
      return true;
    }
    
    // For now, all authenticated users have basic permissions
    // This will be enhanced when we implement full role-based permissions
    if (!user || !organization) {
      return false;
    }
    
    switch (permission) {
      case 'READ':
        return true; // All authenticated users can read
      case 'WRITE':
        return true; // All authenticated users can write (for now)
      case 'DELETE':
        return true; // All authenticated users can delete (for now)
      case 'ADMIN':
        return user.email === '<EMAIL>'; // Only system admin
      default:
        return false;
    }
  };

  const hasRole = (roleName: string): boolean => {
    // For now, simple role checking
    if (user?.email === '<EMAIL>') {
      return roleName === 'SYSTEM_ADMIN' || roleName === 'ORG_ADMIN' || roleName === 'USER';
    }
    
    return roleName === 'USER';
  };

  const isSystemAdmin = (): boolean => {
    return user?.email === '<EMAIL>';
  };

  const isOrgAdmin = (): boolean => {
    // For now, treat system admin as org admin
    return isSystemAdmin();
  };

  return {
    hasPermission,
    hasRole,
    isSystemAdmin,
    isOrgAdmin,
  };
}

/**
 * Hook for organization switching
 */
export function useOrganizationSwitcher() {
  const { organization, organizations, switchOrganization } = useAuthContext();
  
  const canSwitchOrganization = organizations.length > 1;
  
  const switchToOrganization = async (orgId: string) => {
    if (organizations.find(org => org.org_id === orgId)) {
      await switchOrganization(orgId);
    }
  };
  
  return {
    currentOrganization: organization,
    availableOrganizations: organizations,
    canSwitchOrganization,
    switchToOrganization,
  };
}

/**
 * Hook for authentication actions
 */
export function useAuthActions() {
  const { login, register, logout, changePassword, clearError } = useAuthContext();
  
  return {
    login,
    register,
    logout,
    changePassword,
    clearError,
  };
}

/**
 * Hook to get user display information
 */
export function useUserDisplay() {
  const { user, organization } = useAuthContext();
  
  const displayName = user?.full_name || user?.email || 'Unknown User';
  const initials = user?.full_name
    ? user.full_name
        .split(' ')
        .map(name => name.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2)
    : user?.email?.charAt(0).toUpperCase() || '?';
  
  const organizationName = organization?.name || 'No Organization';
  
  return {
    displayName,
    initials,
    organizationName,
    email: user?.email,
    isVerified: user?.is_verified || false,
  };
}
