/**
 * Next.js Middleware for Route Protection
 * 
 * Handles authentication-based redirects at the edge.
 */

import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Routes that require authentication
const PROTECTED_ROUTES = [
  '/projects',
  '/executions',
  '/codegen',
  '/artifacts',
  '/settings',
  '/profile',
  '/organization',
];

// Routes that should redirect authenticated users away (auth pages)
const AUTH_ROUTES = [
  '/auth/login',
  '/auth/register',
];

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/',
  '/about',
  '/pricing',
  '/contact',
  '/terms',
  '/privacy',
  '/auth/verify-email',
  '/auth/forgot-password',
  '/auth/reset-password',
];

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  
  // Get token from cookies or localStorage (we'll use a cookie for SSR)
  const token = request.cookies.get('qak_access_token')?.value;
  const isAuthenticated = !!token;

  // Check if the route is protected
  const isProtectedRoute = PROTECTED_ROUTES.some(route => 
    pathname.startsWith(route)
  );

  // Check if the route is an auth route
  const isAuthRoute = AUTH_ROUTES.some(route => 
    pathname.startsWith(route)
  );

  // Check if the route is public
  const isPublicRoute = PUBLIC_ROUTES.includes(pathname) || 
    pathname.startsWith('/api/') || 
    pathname.startsWith('/_next/') ||
    pathname.includes('.');

  // Redirect unauthenticated users from protected routes to login
  if (isProtectedRoute && !isAuthenticated) {
    const loginUrl = new URL('/auth/login', request.url);
    loginUrl.searchParams.set('redirect', pathname);
    return NextResponse.redirect(loginUrl);
  }

  // Redirect authenticated users from auth routes to projects
  if (isAuthRoute && isAuthenticated) {
    return NextResponse.redirect(new URL('/projects', request.url));
  }

  // Allow all other requests to proceed
  return NextResponse.next();
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public folder files
     */
    '/((?!api|_next/static|_next/image|favicon.ico|.*\\.).*)',
  ],
};
