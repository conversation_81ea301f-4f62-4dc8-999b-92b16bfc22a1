"""Script para ejecutar la API web de QA Agent."""

# Configurar browser_use local ANTES de cualquier import que lo use
import configure_browser_use_local

import os
import sys
import uvicorn
from dotenv import load_dotenv
import warnings
import logfire
from fastapi import FastAPI, Request, HTTPException
from fastapi.responses import JSONResponse, FileResponse, PlainTextResponse
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
import logging

# Initialize root logger configuration early so it captures patched prints
logging.basicConfig(level=logging.DEBUG)

import builtins

# Configure default logger to redirect print statements
logger = logging.getLogger("qak")


def _print_to_logger(*args, **kwargs):
    """Redirect print statements to the standard logger (INFO level)."""
    sep = kwargs.get("sep", " ")
    end = kwargs.get("end", "")
    msg = sep.join(str(a) for a in args) + end
    logger.info(msg)

# Monkey-patch built-in print so Logfire can capture all stdout messages
builtins.print = _print_to_logger

import asyncio
from contextlib import asynccontextmanager

# Cargar variables de entorno
load_dotenv()

# Configure Logfire EARLY - antes de cualquier logging  
# Configure QAK API service (for src/ modules)
logfire.configure(
    service_name="qak-api",
    service_version=os.getenv("LOGFIRE_SERVICE_VERSION", "2.0.0"),
)

# Setup logging with separate handlers for different services
import logging

# Clear any existing handlers to avoid duplicates
logging.getLogger().handlers.clear()

# Create a single handler for QAK API logs
qak_logfire_handler = logfire.LogfireLoggingHandler()

# Configure specific loggers for QAK API service
qak_logger = logging.getLogger("qak")
qak_logger.handlers.clear()  # Clear any existing handlers
qak_logger.addHandler(qak_logfire_handler)
qak_logger.setLevel(logging.DEBUG)
qak_logger.propagate = False  # Don't propagate to avoid duplicates

src_logger = logging.getLogger("src")  
src_logger.handlers.clear()  # Clear any existing handlers
src_logger.addHandler(qak_logfire_handler)
src_logger.setLevel(logging.INFO)
src_logger.propagate = False  # Don't propagate to avoid duplicates

# Configure root logger ONLY for uncaught logs (don't add the same handler)
root_logger = logging.getLogger()
# Only add handler if not already present
if qak_logfire_handler not in root_logger.handlers:
    root_logger.addHandler(qak_logfire_handler)
root_logger.setLevel(logging.DEBUG)

# Suprimir advertencia de LangChain relacionada con convert_system_message_to_human
warnings.filterwarnings(
    "ignore",
    message="Convert_system_message_to_human will be deprecated!",
    category=UserWarning,
    module="langchain_google_genai.chat_models",
)

# Verificar que la API key está configurada
api_key = os.environ.get("GOOGLE_API_KEY")
if not api_key:
    logger.error("Error: No se encontró la API key de Google Gemini. Por favor, configura la variable de entorno GOOGLE_API_KEY.")
    sys.exit(1)

# Configurar los parámetros de ejecución
HOST = os.environ.get("API_HOST", "0.0.0.0")  # Escuchar en todas las interfaces
PORT = int(os.environ.get("API_PORT", 8000))  # Puerto por defecto

# Import routers with correct variable names
from src.api.project_routes import router as projects_router
from src.api.testcase_routes import router as test_cases_router
from src.api.suite_routes import router as suites_router
from src.api.prompt_routes import router as prompts_router
from src.api.story_routes import router as story_router
from src.api.generation_routes import router as generation_router
from src.api.config_routes import router as config_router
from src.api.codegen_routes import router as codegen_router
from src.api.codegen_execution_routes import router as codegen_execution_router
from src.api.analytics_routes import router as analytics_router
from src.api.history_routes import router as history_router
from src.api.v2.execution_routes import router as v2_execution_router
from src.api.script_generation_routes import router as script_generation_router
from src.api.health_routes import router as health_router
from src.api.llm_validation_test_routes import router as llm_test_router
from src.api.llm_health_routes import router as llm_health_router
from src.api.artifact_routes import router as artifact_router
# Correctly import service getters
from src.core.service_container import get_codegen_service
from src.utilities.project_manager_service import ProjectManagerService

# Database initialization imports
from src.database.connection import initialize_database, get_database_manager
from src.database.odm import initialize_odm, register_models
from src.database.models import (
    Project, Execution, CodegenSession, Artifact,
    User, Organization, Role, OrganizationMember
)
from src.core.execution_orchestrator import initialize_orchestrator

# Lifespan management
@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup
    logger.debug("🚀 QAK Server is starting up...")
    
    # Initialize Database Connection and ODM
    logger.debug("🔧 Initializing database connection...")
    try:
        # Initialize database connection
        db_success = await initialize_database()
        if not db_success:
            logger.error("❌ Failed to initialize database connection")
            raise Exception("Database initialization failed")
        
        # Register and initialize Beanie ODM models
        logger.debug("📝 Registering ODM models...")
        models = [
            # Core application models
            Project, Execution, CodegenSession, Artifact,
            # Authentication and multi-tenancy models
            User, Organization, Role, OrganizationMember
        ]
        register_models(models)
        
        logger.debug("🚀 Initializing Beanie ODM...")
        odm_success = await initialize_odm()
        if not odm_success:
            logger.error("❌ Failed to initialize Beanie ODM")
            raise Exception("ODM initialization failed")
            
        logger.debug("✅ Database and ODM initialization completed successfully")
        
    except Exception as e:
        logger.error(f"❌ Database initialization failed: {e}")
        # Don't raise here to allow the server to continue with limited functionality
        logger.warning("⚠️ Server will continue with limited database functionality")
    
    # Pre-load projects
    try:
        # Kick off project preload in background; don't block startup
        async def preload_projects():
            try:
                project_manager = ProjectManagerService()
                projects = await project_manager.get_all_projects()
                logger.debug(f"✅ Loaded {len(projects)} projects from MongoDB.")
            except Exception as e:
                logger.warning(f"⚠️ Could not pre-load projects: {e}")
        asyncio.create_task(preload_projects())
    except Exception as e:
        logger.warning(f"⚠️ Could not pre-load projects: {e}")

    # Initialize PlaywrightCodegenService to clean up old sessions
    try:
        # Initialize codegen service in background
        async def init_codegen():
            try:
                codegen_service = get_codegen_service()
                logger.debug("✅ Codegen service initialized successfully.")
            except Exception as e:
                logger.warning(f"⚠️ Could not initialize codegen service: {e}")
        asyncio.create_task(init_codegen())
    except Exception as e:
        logger.warning(f"⚠️ Could not initialize codegen service: {e}")
        
    # Initialize the Execution Orchestrator
    try:
        # Initialize orchestrator in background
        asyncio.create_task(initialize_orchestrator())
    except Exception as e:
        logger.warning(f"⚠️ Could not initialize execution orchestrator: {e}")

    logger.debug("✅ QAK Server is ready to accept requests.")
    yield
    
    # Shutdown
    logger.debug("👋 QAK Server is shutting down...")
    try:
        db_manager = get_database_manager()
        await db_manager.disconnect()
        logger.debug("✅ Database connection closed cleanly")
    except Exception as e:
        logger.warning(f"⚠️ Error during database cleanup: {e}")

# Initialize FastAPI app
app = FastAPI(
    title="QAK - Quality Assurance Kit",
    description="API for QAK platform, providing services for test automation, generation, and management.",
    version="2.0.0",
    lifespan=lifespan
)

logfire.instrument_fastapi(app)

# Instrument additional services for better observability (only if not already done)
try:
    # Only instrument if not already done by browser_use configuration
    if not os.getenv("BROWSER_USE_LOGFIRE"):
        logfire.instrument_httpx()
        logfire.instrument_openai() 
        logfire.instrument_anthropic()
        logger.debug("🔥 Additional Logfire instrumentation configured")
    else:
        logger.debug("🔥 Additional instrumentation handled by browser_use")
except Exception as e:
    logger.warning(f"⚠️ Some Logfire instrumentations failed: {e}")

# CORS Middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Custom middleware for ngrok browser warning
@app.middleware("http")
async def ngrok_skip_browser_warning(request: Request, call_next):
    response = await call_next(request)
    response.headers["ngrok-skip-browser-warning"] = "true"
    return response

# Configure additional logging levels for better visibility
# Ensure browser_use logs don't interfere with QAK API logs
# browser_use will be handled by its own Logfire configuration with "aery-browser" service
browser_use_logger = logging.getLogger("browser_use")
browser_use_logger.propagate = False  # Prevent propagation to avoid duplicate logs

# Configure MongoDB/PyMongo loggers to reduce verbosity
mongodb_loggers = [
    "pymongo",
    "pymongo.serverSelection", 
    "pymongo.topology",
    "pymongo.connection",
    "pymongo.command",
    "motor"
]
for logger_name in mongodb_loggers:
    mongo_logger = logging.getLogger(logger_name)
    mongo_logger.setLevel(logging.WARNING)  # Only show warnings and errors
    mongo_logger.propagate = False  # Don't propagate to avoid verbose logs

# Configure third-party library loggers for better categorization
third_party_loggers = {
    # HTTP and Network - Set to INFO to see important requests
    "httpx": logging.INFO,
    "httpcore": logging.WARNING,
    "urllib3": logging.WARNING,
    
    # AI/LLM Libraries - Set to INFO for important operations
    "openai": logging.INFO,
    "anthropic": logging.INFO,
    "langchain": logging.INFO,
    "langsmith": logging.WARNING,
    "groq": logging.INFO,
    
    # Browser Automation - Set to INFO for important browser operations  
    "selenium": logging.INFO,
    "playwright": logging.INFO,
    
    # Other third-party - Set to WARNING to reduce noise
    "PIL": logging.WARNING,
    "charset_normalizer": logging.WARNING,
    "trafilatura": logging.WARNING,
    "mem0": logging.WARNING,
}

for logger_name, log_level in third_party_loggers.items():
    third_party_logger = logging.getLogger(logger_name)
    third_party_logger.setLevel(log_level)
    third_party_logger.propagate = False

logger.debug("🔥 QAK API logging configured with Logfire - qak-api service")
logger.debug(f"📊 Logger configuration: QAK={qak_logger.level}, Src={src_logger.level}, Root={root_logger.level}")
logger.debug(f"🔧 Services configured: qak-api (src/*, qak.*), aery-browser (libs/browser_use/*)")
logger.debug("🔇 MongoDB loggers configured to WARNING level to reduce verbosity")
logger.debug("📚 Third-party loggers configured with optimized levels for better categorization")

# Configure static directories
base_dir = os.path.dirname(os.path.abspath(__file__))
screenshots_dir = os.path.join(base_dir, "screenshots")
if not os.path.exists(screenshots_dir):
    os.makedirs(screenshots_dir)
artifacts_dir = os.path.join(base_dir, "artifacts")
if not os.path.exists(artifacts_dir):
    os.makedirs(artifacts_dir)

# API Routers
app.include_router(health_router, prefix="/api")
app.include_router(llm_health_router, prefix="/api/llm")
app.include_router(projects_router, prefix="/api/projects")
app.include_router(suites_router, prefix="/api/projects/{project_id}/suites")
app.include_router(prompts_router, prefix="/api/prompts")
app.include_router(story_router, prefix="/api/stories")
app.include_router(generation_router, prefix="/api/generation")
app.include_router(config_router, prefix="/api/config")
app.include_router(codegen_router, prefix="/api/codegen")
app.include_router(codegen_execution_router, prefix="/api/codegen/execution")
app.include_router(analytics_router, prefix="/api")
app.include_router(history_router, prefix="/api")
app.include_router(llm_test_router, prefix="/api/testing")
app.include_router(artifact_router, prefix="")  # No prefix to handle direct /artifacts/* paths from frontend

# V2 Routes
app.include_router(v2_execution_router, prefix="/api/v2/tests")
app.include_router(script_generation_router)

# V1 Routes (legacy) - TestExecutor legacy routes removed, keeping project management routes
app.include_router(test_cases_router)

# Mount static directories after API routes so they don't intercept API requests
app.mount("/screenshots", StaticFiles(directory=screenshots_dir), name="screenshots")
# Don't mount /artifacts directory - we're handling it with artifact_router now

# Root endpoint
@app.get("/", response_class=PlainTextResponse, tags=["Root"])
async def root():
    return "Welcome to QAK API"

# Test endpoint for Logfire logging verification
@app.get("/test-logfire")
async def test_logfire():
    """Test endpoint to verify that logs are correctly routed and categorized by level"""
    import logging
    
    # Test QAK API logs with different levels (should appear under qak-api service)
    test_logger = logging.getLogger("qak.test")
    test_logger.debug("🐛 [QAK-API] [DEBUG] Debug log from QAK API - development info")
    test_logger.info("ℹ️ [QAK-API] [INFO] Info log from QAK API - normal operation")
    test_logger.warning("⚠️ [QAK-API] [WARNING] Warning log from QAK API - potential issue")
    test_logger.error("❌ [QAK-API] [ERROR] Error log from QAK API - something went wrong")
    
    # Test main QAK logger with different categories
    logger.info("🔥 [QAK-API] [INFO] Main QAK logger test - application startup")
    logger.debug("🔍 [QAK-API] [DEBUG] Configuration details loaded successfully")
    
    # Test src module logger with service operations
    src_logger = logging.getLogger("src.test")
    src_logger.info("📦 [QAK-API] [INFO] Service operation completed successfully")
    src_logger.warning("⚠️ [QAK-API] [WARNING] Service operation took longer than expected")
    
    # Test service-specific loggers
    service_logger = logging.getLogger("src.services.test")
    service_logger.info("�️ [QAK-API] [INFO] Service business logic executed")
    service_logger.debug("🔍 [QAK-API] [DEBUG] Service internal state updated")
    
    # Test database loggers
    db_logger = logging.getLogger("src.database.test")
    db_logger.info("🗄️ [QAK-API] [INFO] Database operation completed")
    db_logger.error("❌ [QAK-API] [ERROR] Database connection failed")
    
    # Test browser_use logger (should appear under aery-browser service)
    browser_logger = logging.getLogger("browser_use.test")
    browser_logger.info("🌐 [AERY-BROWSER] [INFO] Browser automation operation")
    browser_logger.warning("⚠️ [AERY-BROWSER] [WARNING] Browser operation timeout")
    
    # Test libs logger (should appear under aery-browser service)
    libs_logger = logging.getLogger("libs.test")
    libs_logger.info("📚 [AERY-BROWSER] [INFO] Library function executed")
    libs_logger.debug("🔍 [AERY-BROWSER] [DEBUG] Library internal processing")
    
    return {
        "message": "Logfire service separation and log categorization test completed",
        "services_configured": {
            "qak-api": "Handles src/*, qak.*, and main application logs",
            "aery-browser": "Handles browser_use.* and libs/* logs"
        },
        "log_categories_tested": {
            "DEBUG": "🐛🔍 Development and troubleshooting information",
            "INFO": "ℹ️🔥📦🛠️🗄️🌐📚 Normal operation and business logic",
            "WARNING": "⚠️ Potential issues and performance concerns", 
            "ERROR": "❌ Actual errors and failures"
        },
        "expected_routing": {
            "qak-api_service": [
                "[DEBUG] Configuration and development logs",
                "[INFO] Application startup and service operations", 
                "[WARNING] Performance and operational warnings",
                "[ERROR] Application and database errors"
            ],
            "aery-browser_service": [
                "[INFO] Browser automation and library operations",
                "[WARNING] Browser timeouts and automation issues",
                "[DEBUG] Library internal processing details"
            ]
        },
        "log_levels_configured": {
            "qak": "DEBUG (10) - All application logs",
            "src": "INFO (20) - Service operations and above",
            "third_party": "INFO/WARNING - Optimized per library",
            "mongodb": "WARNING (30) - Only warnings and errors"
        }
    }

# Punto de entrada principal
def main():
    logger.info(f"Iniciando API web de QA Agent en http://{HOST}:{PORT}...")
    logger.info("Documentacion API disponible en:")
    logger.info(f"  - Swagger UI: http://localhost:{PORT}/docs")
    logger.info(f"  - ReDoc: http://localhost:{PORT}/redoc")
    logger.info("\nPresiona Ctrl+C para detener el servidor.")
    
    # Ejecutar la aplicacion FastAPI con Uvicorn
    uvicorn.run(
        "app:app",  # Cambiado para usar la aplicacion actual en lugar de src.api.api:app
        host=HOST,
        port=PORT,
        reload=False,  # Desactivar reload para evitar problemas de multiprocessing
        workers=1,     # Un solo worker para evitar conflictos
        log_level="info",
        access_log=True
    )

if __name__ == "__main__":
    main()