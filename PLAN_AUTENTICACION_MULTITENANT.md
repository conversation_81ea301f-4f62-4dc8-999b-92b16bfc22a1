# Plan de Implementación: Sistema de Autenticación y Autorización Multi-Tenant

## **Resumen Ejecutivo**

Este documento detalla la implementación completa de un sistema de autenticación y autorización multi-tenant para la aplicación QAK, diseñado para permitir que equipos y startups utilicen la plataforma de manera aislada y segura.

**Objetivo**: Transformar QAK de una aplicación monousuario a una plataforma multi-tenant con organizaciones, roles y permisos granulares.

**Stack Recomendado**: 
- Backend: FastAPI + JWT + bcrypt + MongoDB
- Frontend: Next.js + Context API + TanStack Query
- Base de datos: MongoDB con modelos extendidos

---

## **Fase 1: Diseño y Modelos de Base de Datos** 

### 📋 **1.1 Modelos de Autenticación** 

#### ✅ **Crear modelo User**
- [x] Archivo: `src/database/models/user.py`
- [ ] Campos: user_id, email, password_hash, full_name, created_at, updated_at, is_active, email_verified
- [ ] Validaciones: email único, password seguro, normalización de email
- [ ] Índices: email (único), user_id, created_at

#### ✅ **Crear modelo Organization**
- [x] Archivo: `src/database/models/organization.py`
- [ ] Campos: org_id, name, slug, description, created_at, updated_at, is_active, plan_type
- [ ] Validaciones: slug único, name requerido
- [ ] Índices: org_id (único), slug (único), created_at

#### ✅ **Crear modelo Role**
- [x] Archivo: `src/database/models/role.py`
- [ ] Roles predefinidos: USER, ORG_ADMIN, ADMIN
- [ ] Campos: role_id, name, description, permissions, is_system_role
- [ ] Permisos: read, write, delete, admin (por recurso)

#### ✅ **Crear modelo OrganizationMember**
- [x] Archivo: `src/database/models/organization_member.py`
- [ ] Campos: member_id, user_id, org_id, role_id, joined_at, invited_by, is_active
- [ ] Relaciones: User, Organization, Role
- [ ] Índices: (user_id, org_id) único, org_id, user_id

### 📋 **1.2 Modelos Extendidos para Multi-tenancy**

#### ✅ **Modificar modelo Project**
- [ ] Agregar campo: organization_id (Indexed)
- [ ] Agregar campo: created_by (user_id)
- [ ] Actualizar índices para incluir organization_id
- [ ] Validar que user pertenezca a la organización

#### ✅ **Modificar modelo Execution**
- [ ] Agregar campo: organization_id (Indexed)
- [ ] Agregar campo: executed_by (user_id)
- [ ] Filtros automáticos por organización

#### ✅ **Modificar modelo CodegenSession**
- [ ] Agregar campo: organization_id (Indexed)
- [ ] Agregar campo: created_by (user_id)

#### ✅ **Modificar modelo Artifact**
- [ ] Agregar campo: organization_id (Indexed)
- [ ] Agregar campo: uploaded_by (user_id)

---

## **Fase 2: Servicios de Autenticación Backend** 

### 📋 **2.1 Servicios Core**

#### ✅ **AuthService - Autenticación principal**
- [ ] Archivo: `src/services/auth_service.py`
- [ ] **Funciones**:
  - [ ] `hash_password(password: str) -> str` - bcrypt
  - [ ] `verify_password(password: str, hash: str) -> bool`
  - [ ] `create_access_token(user_id: str, org_id: str) -> str` - JWT
  - [ ] `verify_token(token: str) -> dict` - Validar JWT
  - [ ] `refresh_token(refresh_token: str) -> str`
  - [ ] `register_user(email, password, full_name, org_name?) -> User`
  - [ ] `login_user(email, password) -> dict` - User + tokens
  - [ ] `logout_user(token: str) -> bool` - Blacklist token

#### ✅ **UserService - Gestión de usuarios**
- [ ] Archivo: `src/services/user_service.py`
- [ ] **Funciones**:
  - [ ] `get_user_by_id(user_id: str) -> User`
  - [ ] `get_user_by_email(email: str) -> User`
  - [ ] `update_user_profile(user_id: str, data: dict) -> User`
  - [ ] `change_password(user_id: str, old_pass: str, new_pass: str) -> bool`
  - [ ] `deactivate_user(user_id: str) -> bool`
  - [ ] `get_user_organizations(user_id: str) -> List[Organization]`

#### ✅ **OrganizationService - Gestión de organizaciones**
- [ ] Archivo: `src/services/organization_service.py`
- [ ] **Funciones**:
  - [ ] `create_organization(name: str, created_by: str) -> Organization`
  - [ ] `get_organization(org_id: str) -> Organization`
  - [ ] `update_organization(org_id: str, data: dict) -> Organization`
  - [ ] `add_member(org_id: str, email: str, role: str, invited_by: str) -> OrganizationMember`
  - [ ] `remove_member(org_id: str, user_id: str) -> bool`
  - [ ] `change_member_role(org_id: str, user_id: str, new_role: str) -> bool`
  - [ ] `get_organization_members(org_id: str) -> List[OrganizationMember]`

### 📋 **2.2 Repositorios de Base de Datos** ⏱️ 3-4 días

#### ✅ **UserRepository**
- [ ] Archivo: `src/database/repositories/user_repository.py`
- [ ] Extender BaseRepository
- [ ] CRUD completo para usuarios
- [ ] Métodos: `find_by_email`, `find_active_users`, `update_last_login`

#### ✅ **OrganizationRepository**
- [ ] Archivo: `src/database/repositories/organization_repository.py`
- [ ] CRUD para organizaciones
- [ ] Métodos: `find_by_slug`, `get_organization_stats`

#### ✅ **OrganizationMemberRepository**
- [ ] Archivo: `src/database/repositories/organization_member_repository.py`
- [ ] Gestión de membresías
- [ ] Métodos: `find_by_user_and_org`, `get_user_organizations`, `get_org_members`

---

## **Fase 3: Middleware y Endpoints de Autenticación** 

### 📋 **3.1 Middleware de Autenticación**

#### ✅ **AuthMiddleware - Middleware principal**
- [ ] Archivo: `src/core/auth_middleware.py`
- [ ] **Funcionalidades**:
  - [ ] Extraer token JWT del header Authorization
  - [ ] Validar token y extraer user_id, org_id
  - [ ] Inyectar user y organization en request.state
  - [ ] Manejar rutas públicas (lista de exclusiones)
  - [ ] Respuestas 401 para tokens inválidos

#### ✅ **RoleMiddleware - Middleware de autorización**
- [ ] Archivo: `src/core/role_middleware.py`
- [ ] **Funcionalidades**:
  - [ ] Validar permisos basados en roles
  - [ ] Decorador `@require_role(role: str)`
  - [ ] Decorador `@require_permission(permission: str)`
  - [ ] Validar membresía en organización
  - [ ] Respuestas 403 para permisos insuficientes

#### ✅ **OrganizationMiddleware - Aislamiento de datos**
- [ ] Archivo: `src/core/organization_middleware.py`
- [ ] **Funcionalidades**:
  - [ ] Inyectar filtros automáticos por organization_id
  - [ ] Validar que los recursos pertenezcan a la organización del usuario
  - [ ] Bypass para usuarios ADMIN (ver todas las organizaciones)

### 📋 **3.2 Endpoints de Autenticación**

#### ✅ **Auth Routes**
- [ ] Archivo: `src/api/auth_routes.py`
- [ ] **Endpoints**:
  - [ ] `POST /auth/register` - Registro con organización
  - [ ] `POST /auth/login` - Login con tokens JWT
  - [ ] `POST /auth/logout` - Logout y blacklist token
  - [ ] `POST /auth/refresh` - Refresh token
  - [ ] `GET /auth/me` - Perfil del usuario actual
  - [ ] `PUT /auth/me` - Actualizar perfil
  - [ ] `POST /auth/change-password` - Cambiar contraseña

#### ✅ **Organization Routes**
- [ ] Archivo: `src/api/organization_routes.py`
- [ ] **Endpoints**:
  - [ ] `GET /organizations` - Listar organizaciones del usuario
  - [ ] `POST /organizations` - Crear nueva organización
  - [ ] `GET /organizations/{org_id}` - Detalles de organización
  - [ ] `PUT /organizations/{org_id}` - Actualizar organización
  - [ ] `GET /organizations/{org_id}/members` - Listar miembros
  - [ ] `POST /organizations/{org_id}/members` - Invitar miembro
  - [ ] `PUT /organizations/{org_id}/members/{user_id}` - Cambiar rol
  - [ ] `DELETE /organizations/{org_id}/members/{user_id}` - Remover miembro

#### ✅ **User Management Routes**
- [ ] Archivo: `src/api/user_routes.py`
- [ ] **Endpoints** (solo para ADMIN):
  - [ ] `GET /admin/users` - Listar todos los usuarios
  - [ ] `GET /admin/organizations` - Listar todas las organizaciones
  - [ ] `PUT /admin/users/{user_id}/deactivate` - Desactivar usuario
  - [ ] `GET /admin/stats` - Estadísticas del sistema

---

## **Fase 4: Aislamiento de Datos Multi-tenant** 

### 📋 **4.1 Modificación de Servicios Existentes**

#### ✅ **ProjectService - Filtrado por organización**
- [ ] Modificar métodos para incluir organization_id automáticamente
- [ ] `create_project()` - Usar org_id del usuario autenticado
- [ ] `get_projects()` - Filtrar por organization_id
- [ ] `get_project(project_id)` - Validar que pertenezca a la organización
- [ ] Agregar validación de permisos en escritura

#### ✅ **ExecutionService - Filtrado por organización**
- [ ] Filtrar ejecuciones por organization_id
- [ ] Validar acceso a ejecuciones de la organización
- [ ] Agregar executed_by en nuevas ejecuciones

#### ✅ **CodegenService - Filtrado por organización**
- [ ] Filtrar sesiones por organization_id
- [ ] Validar acceso a sesiones de codegen

#### ✅ **ArtifactService - Filtrado por organización**
- [ ] Filtrar artifacts por organization_id
- [ ] Validar acceso a archivos de la organización

### 📋 **4.2 Actualización de Endpoints Existentes**

#### ✅ **Project Routes - Protección con auth**
- [ ] Aplicar middleware de autenticación
- [ ] Validar membresía en organización
- [ ] Filtrado automático por organization_id

#### ✅ **Test Execution Routes - Protección con auth**
- [ ] Aplicar middleware de autenticación
- [ ] Validar que las ejecuciones pertenezcan a la organización

#### ✅ **Codegen Routes - Protección con auth**
- [ ] Aplicar middleware de autenticación  
- [ ] Filtrado por organización

---

## **Fase 5: Frontend - Autenticación y UI** 

### 📋 **5.1 Context y Estado Global**

#### ✅ **AuthContext - Gestión de estado de autenticación**
- [ ] Archivo: `web/src/contexts/AuthContext.tsx`
- [ ] **Estado**:
  - [ ] `user: User | null` - Usuario actual
  - [ ] `organization: Organization | null` - Organización activa
  - [ ] `organizations: Organization[]` - Organizaciones del usuario
  - [ ] `isAuthenticated: boolean` - Estado de autenticación
  - [ ] `isLoading: boolean` - Estado de carga
- [ ] **Funciones**:
  - [ ] `login(email, password)` - Autenticar usuario
  - [ ] `logout()` - Cerrar sesión
  - [ ] `register(data)` - Registrar usuario y organización
  - [ ] `switchOrganization(orgId)` - Cambiar organización activa
  - [ ] `refreshUser()` - Recargar datos del usuario

#### ✅ **AuthProvider - Provider del contexto**
- [ ] Archivo: `web/src/providers/AuthProvider.tsx`
- [ ] Gestión del token JWT en localStorage/cookies
- [ ] Interceptor de axios para inyectar tokens automáticamente
- [ ] Manejo de refresh token automático
- [ ] Redirección automática en expiración de sesión

#### ✅ **API Client Authentication**
- [ ] Archivo: `web/src/lib/auth-api.ts`
- [ ] **Funciones**:
  - [ ] `login(email, password)` - Llamada de login
  - [ ] `register(userData)` - Llamada de registro
  - [ ] `logout()` - Llamada de logout
  - [ ] `getMe()` - Obtener perfil actual
  - [ ] `refreshToken()` - Renovar token
  - [ ] `changePassword(oldPass, newPass)` - Cambiar contraseña

### 📋 **5.2 Componentes de Autenticación**

#### ✅ **LoginForm - Formulario de inicio de sesión**
- [ ] Archivo: `web/src/components/auth/LoginForm.tsx`
- [ ] **Características**:
  - [ ] React Hook Form + Zod validation
  - [ ] Campos: email, password
  - [ ] Validación de formato de email
  - [ ] Manejo de errores de API
  - [ ] Loading states
  - [ ] Link a registro

#### ✅ **RegisterForm - Formulario de registro**
- [ ] Archivo: `web/src/components/auth/RegisterForm.tsx`
- [ ] **Características**:
  - [ ] Campos: full_name, email, password, confirm_password, organization_name
  - [ ] Validación de contraseña segura
  - [ ] Confirmación de contraseña
  - [ ] Términos y condiciones
  - [ ] Link a login

#### ✅ **ProtectedRoute - Wrapper para rutas protegidas**
- [ ] Archivo: `web/src/components/auth/ProtectedRoute.tsx`
- [ ] **Características**:
  - [ ] Redireccionar a /login si no autenticado
  - [ ] Mostrar loading mientras valida autenticación
  - [ ] Validar permisos específicos (opcional)
  - [ ] Soporte para roles requeridos

#### ✅ **OrganizationSwitcher - Selector de organización**
- [ ] Archivo: `web/src/components/auth/OrganizationSwitcher.tsx`
- [ ] **Características**:
  - [ ] Dropdown con organizaciones del usuario
  - [ ] Cambio de organización activa
  - [ ] Indicador visual de organización actual
  - [ ] Opción "Crear nueva organización"

### 📋 **5.3 Páginas de Autenticación** ⏱️ 2-3 días

#### ✅ **Login Page**
- [ ] Archivo: `web/src/app/login/page.tsx`
- [ ] Layout sin sidebar
- [ ] Formulario centrado
- [ ] Branding de QAK
- [ ] Links a registro y recuperación

#### ✅ **Register Page**
- [ ] Archivo: `web/src/app/register/page.tsx`
- [ ] Layout sin sidebar  
- [ ] Formulario de registro completo
- [ ] Explicación del modelo de organizaciones

#### ✅ **Profile Page**
- [ ] Archivo: `web/src/app/profile/page.tsx`
- [ ] Edición de perfil de usuario
- [ ] Cambio de contraseña
- [ ] Gestión de organizaciones

---

## **Fase 6: Gestión de Organizaciones UI** 

### 📋 **6.1 Páginas de Gestión**

#### ✅ **Organization Dashboard**
- [ ] Archivo: `web/src/app/organization/page.tsx`
- [ ] Vista general de la organización actual
- [ ] Estadísticas: proyectos, usuarios, ejecuciones
- [ ] Actividad reciente de la organización

#### ✅ **Organization Settings**
- [ ] Archivo: `web/src/app/organization/settings/page.tsx`
- [ ] **Funcionalidades**:
  - [ ] Editar nombre y descripción de organización
  - [ ] Configuración de límites y permisos
  - [ ] Configuración de integraciones
  - [ ] Solo para ORG_ADMIN

#### ✅ **Members Management**
- [ ] Archivo: `web/src/app/organization/members/page.tsx`
- [ ] **Funcionalidades**:
  - [ ] Lista de miembros con roles
  - [ ] Invitar nuevos miembros por email
  - [ ] Cambiar roles de miembros existentes
  - [ ] Remover miembros
  - [ ] Solo para ORG_ADMIN

### 📋 **6.2 Componentes de Organización**

#### ✅ **MembersList**
- [ ] Archivo: `web/src/components/organization/MembersList.tsx`
- [ ] Tabla con miembros, roles y acciones
- [ ] Filtros por rol
- [ ] Acciones en línea: cambiar rol, remover

#### ✅ **InviteMemberForm**
- [ ] Archivo: `web/src/components/organization/InviteMemberForm.tsx`
- [ ] Modal para invitar nuevos miembros
- [ ] Selección de rol para el nuevo miembro
- [ ] Validación de email

#### ✅ **OrganizationCard**
- [ ] Archivo: `web/src/components/organization/OrganizationCard.tsx`
- [ ] Tarjeta con información de organización
- [ ] Estadísticas básicas
- [ ] Acciones rápidas

---

## **Fase 7: Protección de Rutas y Permisos** 

### 📋 **7.1 Protección Backend**

#### ✅ **Aplicar middleware a todas las rutas**
- [ ] Configurar middleware en `src/api/api.py`
- [ ] Lista de rutas públicas: `/auth/*`, `/health`, `/docs`
- [ ] Aplicar AuthMiddleware globalmente
- [ ] Aplicar OrganizationMiddleware a rutas de recursos

#### ✅ **Validación de permisos en endpoints**
- [ ] Decoradores de roles en endpoints críticos
- [ ] Validación de membresía en organización
- [ ] Permisos específicos para operaciones destructivas

### 📋 **7.2 Protección Frontend**

#### ✅ **Proteger rutas en App Router**
- [ ] Envolver layout principal con ProtectedRoute
- [ ] Excluir rutas públicas: `/login`, `/register`
- [ ] Redirección automática a login

#### ✅ **Protección de componentes**
- [ ] Componentes que requieren roles específicos
- [ ] Ocultación de botones según permisos
- [ ] Validación de organización activa

---

## **Fase 8: Migración de Datos Existentes** 

### 📋 **8.1 Scripts de Migración**

#### ✅ **Migración de proyectos existentes**
- [ ] Archivo: `scripts/migrate_projects_to_multitenant.py`
- [ ] **Funcionalidades**:
  - [ ] Crear organización por defecto "Default Organization"
  - [ ] Usuario admin por defecto
  - [ ] Asignar todos los proyectos existentes a organización por defecto
  - [ ] Agregar organization_id a todos los registros existentes

#### ✅ **Migración de ejecuciones**
- [ ] Asignar organization_id a todas las ejecuciones existentes
- [ ] Asignar executed_by al usuario admin por defecto

#### ✅ **Migración de sesiones de codegen**
- [ ] Asignar organization_id y created_by a sesiones existentes

#### ✅ **Migración de artifacts**
- [ ] Asignar organization_id y uploaded_by a artifacts existentes

### 📋 **8.2 Validación y Testing** 

#### ✅ **Validar integridad de datos migrados**
- [ ] Script de validación post-migración
- [ ] Verificar que todos los registros tienen organization_id
- [ ] Verificar relaciones user-organization

#### ✅ **Backup y rollback**
- [ ] Script de backup pre-migración
- [ ] Procedimiento de rollback en caso de errores

---

## **Fase 9: Testing y Seguridad** 

### 📋 **9.1 Tests de Seguridad** 

#### ✅ **Tests de autenticación**
- [ ] Archivo: `tests/test_auth_security.py`
- [ ] **Casos de prueba**:
  - [ ] Login con credenciales válidas/inválidas
  - [ ] Registro con datos válidos/duplicados
  - [ ] Validación de tokens JWT
  - [ ] Expiración de tokens
  - [ ] Refresh token flow

#### ✅ **Tests de autorización**
- [ ] Archivo: `tests/test_authorization.py`
- [ ] **Casos de prueba**:
  - [ ] Acceso a recursos propios vs ajenos
  - [ ] Validación de roles y permisos
  - [ ] Aislamiento entre organizaciones
  - [ ] Escalación de privilegios

#### ✅ **Tests de aislamiento multi-tenant**
- [ ] Archivo: `tests/test_multitenant_isolation.py`
- [ ] **Casos de prueba**:
  - [ ] Usuario de org A no puede ver datos de org B
  - [ ] Filtrado automático por organization_id
  - [ ] ADMIN puede ver todas las organizaciones
  - [ ] Cross-tenant data leakage prevention

### 📋 **9.2 Tests de Integración** 

#### ✅ **Tests end-to-end**
- [ ] Flujo completo: registro → login → crear proyecto → ejecutar prueba
- [ ] Cambio de organización y aislamiento de datos
- [ ] Gestión de miembros y roles

#### ✅ **Tests de performance**
- [ ] Impacto del middleware en latencia
- [ ] Performance de queries con filtros de organización
- [ ] Carga de múltiples usuarios/organizaciones

---

## **Fase 10: Documentación y Deployment** 

### 📋 **10.1 Documentación**

#### ✅ **Documentación técnica**
- [ ] Archivo: `docs/authentication.md`
- [ ] Documentación de APIs de autenticación
- [ ] Guía de roles y permisos
- [ ] Arquitectura de multi-tenancy

#### ✅ **Guía de usuario**
- [ ] Archivo: `docs/user_guide.md`
- [ ] Registro e inicio de sesión
- [ ] Gestión de organizaciones
- [ ] Invitación de miembros
- [ ] Cambio entre organizaciones

#### ✅ **Guía de administración**
- [ ] Archivo: `docs/admin_guide.md`
- [ ] Gestión de usuarios del sistema
- [ ] Monitoreo de organizaciones
- [ ] Procedimientos de backup y migración

### 📋 **10.2 Variables de Entorno** 

#### ✅ **Configuración de autenticación**
- [ ] Actualizar `env.example` con nuevas variables:
  - [ ] `JWT_SECRET_KEY` - Clave secreta para JWT
  - [ ] `JWT_ALGORITHM="HS256"` - Algoritmo JWT
  - [ ] `JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30` - Expiración de token
  - [ ] `JWT_REFRESH_TOKEN_EXPIRE_DAYS=7` - Expiración de refresh token
  - [ ] `BCRYPT_ROUNDS=12` - Rounds de bcrypt
  - [ ] `DEFAULT_ADMIN_EMAIL` - Email del admin por defecto
  - [ ] `DEFAULT_ADMIN_PASSWORD` - Password del admin por defecto

### 📋 **10.3 Deployment**

#### ✅ **Actualización de Docker**
- [ ] Actualizar `Dockerfile` con nuevas dependencias
- [ ] Actualizar `docker-compose.yml` con variables de entorno
- [ ] Script de inicialización con usuario admin

#### ✅ **Database initialization**
- [ ] Script de inicialización de roles por defecto
- [ ] Creación de usuario admin inicial
- [ ] Índices de base de datos

---

## **Cronograma Estimado**

| Fase | Descripción | Duración | Dependencias |
|------|-------------|----------|--------------|
| 1 | Diseño y Modelos de Base de Datos | 5-7 días | - |
| 2 | Servicios de Autenticación Backend | 7-9 días | Fase 1 |
| 3 | Middleware y Endpoints | 6-8 días | Fase 2 |
| 4 | Aislamiento Multi-tenant | 7-9 días | Fase 3 |
| 5 | Frontend - Autenticación y UI | 9-12 días | Fase 3 |
| 6 | Gestión de Organizaciones UI | 7-9 días | Fase 5 |
| 7 | Protección de Rutas | 4-6 días | Fases 5-6 |
| 8 | Migración de Datos | 5-7 días | Fase 4 |
| 9 | Testing y Seguridad | 5-7 días | Fases 7-8 |
| 10 | Documentación y Deployment | 5-7 días | Fase 9 |

**Duración Total Estimada: 10-14 semanas**

---

## **Consideraciones de Seguridad**

### 🔒 **Mejores Prácticas Implementadas**

#### ✅ **Autenticación Segura**
- [ ] Passwords hasheados con bcrypt (12 rounds mínimo)
- [ ] JWT con expiración corta (30 minutos)
- [ ] Refresh tokens con expiración más larga (7 días)
- [ ] Blacklist de tokens en logout
- [ ] Rate limiting en endpoints de auth

#### ✅ **Autorización Robusta**
- [ ] Principio de menor privilegio
- [ ] Validación de permisos en cada request
- [ ] Aislamiento estricto entre organizaciones
- [ ] Roles predefinidos inmutables

#### ✅ **Protección contra Ataques**
- [ ] Protection contra CSRF (SameSite cookies)
- [ ] Validación de input con Pydantic
- [ ] Sanitización de queries de base de datos
- [ ] Headers de seguridad (CORS, HSTS, etc.)
- [ ] Rate limiting por IP y usuario

#### ✅ **Manejo de Sesiones**
- [ ] Tokens JWT stateless
- [ ] Invalidación de sesiones comprometidas
- [ ] Monitoreo de logins simultáneos
- [ ] Expiración automática de sesiones inactivas

---

## **Modelo de Datos Final**

```
User
├── user_id (UUID, unique)
├── email (string, unique)
├── password_hash (string)
├── full_name (string)
├── is_active (boolean)
├── email_verified (boolean)
├── created_at (datetime)
└── updated_at (datetime)

Organization
├── org_id (UUID, unique)
├── name (string)
├── slug (string, unique)
├── description (string)
├── plan_type (enum: free, pro, enterprise)
├── is_active (boolean)
├── created_at (datetime)
└── updated_at (datetime)

OrganizationMember
├── member_id (UUID, unique)
├── user_id (UUID, FK → User)
├── org_id (UUID, FK → Organization)
├── role_id (UUID, FK → Role)
├── joined_at (datetime)
├── invited_by (UUID, FK → User)
└── is_active (boolean)

Role
├── role_id (UUID, unique)
├── name (string: USER, ORG_ADMIN, ADMIN)
├── description (string)
├── permissions (array: read, write, delete, admin)
└── is_system_role (boolean)

Project (modificado)
├── project_id (UUID, unique)
├── organization_id (UUID, FK → Organization) ← NUEVO
├── created_by (UUID, FK → User) ← NUEVO
├── name (string)
├── description (string)
├── tags (array)
├── github_config (object)
├── created_at (datetime)
└── updated_at (datetime)

Execution (modificado)
├── execution_id (UUID, unique)
├── organization_id (UUID, FK → Organization) ← NUEVO
├── executed_by (UUID, FK → User) ← NUEVO
├── test_type (string)
├── project_id (UUID, FK → Project)
├── status (string)
├── [... campos existentes ...]

CodegenSession (modificado)
├── session_id (UUID, unique)
├── organization_id (UUID, FK → Organization) ← NUEVO
├── created_by (UUID, FK → User) ← NUEVO
├── [... campos existentes ...]

Artifact (modificado)
├── artifact_id (UUID, unique)
├── organization_id (UUID, FK → Organization) ← NUEVO
├── uploaded_by (UUID, FK → User) ← NUEVO
├── [... campos existentes ...]
```

---

## **APIs de Autenticación**

### 🔌 **Endpoints Principales**

```bash
# Autenticación
POST   /auth/register           # Registro de usuario + organización
POST   /auth/login              # Login con email/password
POST   /auth/logout             # Logout y blacklist token
POST   /auth/refresh            # Refresh access token
GET    /auth/me                 # Perfil del usuario actual
PUT    /auth/me                 # Actualizar perfil
POST   /auth/change-password    # Cambiar contraseña

# Organizaciones
GET    /organizations           # Listar organizaciones del usuario
POST   /organizations           # Crear nueva organización
GET    /organizations/{org_id}  # Detalles de organización
PUT    /organizations/{org_id}  # Actualizar organización
DELETE /organizations/{org_id}  # Eliminar organización (ORG_ADMIN)

# Gestión de miembros
GET    /organizations/{org_id}/members           # Listar miembros
POST   /organizations/{org_id}/members          # Invitar miembro
PUT    /organizations/{org_id}/members/{user_id} # Cambiar rol
DELETE /organizations/{org_id}/members/{user_id} # Remover miembro

# Administración (solo ADMIN)
GET    /admin/users             # Listar todos los usuarios
GET    /admin/organizations     # Listar todas las organizaciones
PUT    /admin/users/{user_id}/deactivate # Desactivar usuario
GET    /admin/stats             # Estadísticas del sistema
```

---

## **Checklist de Validación Final**

### ✅ **Funcionalidad Básica**
- [ ] Usuario puede registrarse con organización
- [ ] Usuario puede hacer login/logout
- [ ] Usuario puede crear proyectos en su organización
- [ ] Usuario solo ve datos de su organización
- [ ] Usuario puede cambiar entre organizaciones (si pertenece a varias)

### ✅ **Roles y Permisos**
- [ ] USER: puede crear/ver/editar recursos en su organización
- [ ] ORG_ADMIN: puede gestionar miembros de su organización
- [ ] ADMIN: puede ver/gestionar todas las organizaciones

### ✅ **Seguridad**
- [ ] Passwords hasheados correctamente
- [ ] Tokens JWT válidos y con expiración
- [ ] Aislamiento completo entre organizaciones
- [ ] No hay data leakage entre organizaciones
- [ ] Middleware funciona correctamente

### ✅ **User Experience**
- [ ] Flujo de registro intuitivo
- [ ] Login/logout funcionan sin problemas
- [ ] Selector de organización disponible
- [ ] Gestión de miembros para ORG_ADMIN
- [ ] Interfaz responsiva y accesible

### ✅ **Migración de Datos**
- [ ] Datos existentes migrados correctamente
- [ ] Todos los registros tienen organization_id
- [ ] Usuario admin por defecto creado
- [ ] Organización por defecto asignada

---

## **Recursos Adicionales**

### 📚 **Dependencias a Instalar**

```bash
# Backend
pip install python-jose[cryptography]  # JWT
pip install passlib[bcrypt]            # Password hashing
pip install python-multipart          # Form handling

# Frontend
npm install js-cookie @types/js-cookie # Cookie management
npm install axios                      # HTTP client
```

### 🔧 **Variables de Entorno Nuevas**

```bash
# JWT Configuration
JWT_SECRET_KEY="tu-clave-secreta-muy-segura-aqui-256-bits"
JWT_ALGORITHM=HS256
JWT_ACCESS_TOKEN_EXPIRE_MINUTES=30
JWT_REFRESH_TOKEN_EXPIRE_DAYS=7

# Password Hashing
BCRYPT_ROUNDS=12

# Default Admin
DEFAULT_ADMIN_EMAIL=<EMAIL>
DEFAULT_ADMIN_PASSWORD=admin123456

# Security
ENABLE_CSRF_PROTECTION=true
ENABLE_RATE_LIMITING=true
RATE_LIMIT_PER_MINUTE=60
```

---

**¡Este plan está listo para implementación! Cada checklist item puede ser implementado de forma incremental, permitiendo testing continuo y deployment progresivo.** 