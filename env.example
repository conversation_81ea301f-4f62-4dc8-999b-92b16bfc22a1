# ===============================================================
# QAK Platform - Environment Variables
# ===============================================================
#
# -> To use, rename this file to ".env"
# -> Then, fill in the required GOOGLE_API_KEY.
# -> Uncomment optional features as needed.
#
# ===============================================================

# ---------------------------------------------------------------
# REQUIRED: Core AI Configuration
# ---------------------------------------------------------------
# Get your free API key from Google AI Studio: https://aistudio.google.com/app/apikey
GOOGLE_API_KEY=your_google_api_key_here

# Main model to use for AI operations
LLM_MODEL=gemini-2.5-flash

# ---------------------------------------------------------------
# OPTIONAL: Additional AI Providers
# ---------------------------------------------------------------
# Uncomment and add your API keys to enable other AI models.
#OPENAI_API_KEY=
#ANTHROPIC_API_KEY=
#GROQ_API_KEY=

# ---------------------------------------------------------------
# OPTIONAL: Language & GitHub Settings
# ---------------------------------------------------------------
# Language for prompts. Options: en (English), es (Spanish)
PROMPT_LANGUAGE=es

# For Pull Request analysis features.
#GITHUB_TOKEN=
#GITHUB_REPO=owner/repository

# ===============================================================
# ADVANCED: Browser Automation Configuration
# ===============================================================
# Default settings are optimized for Gemini 2.5 Flash.
# Adjust these only if you know what you are doing.
# ---------------------------------------------------------------

# --- API Rate Limiting (to prevent errors) ---
QAK_API_CALL_DELAY=10
BROWSER_USE_GEMINI_RPM=12
BROWSER_USE_GEMINI_TPM=800000

# --- Strategic Planner ---
BROWSER_USE_PLANNER_INTERVAL=3
BROWSER_USE_PLANNER_REASONING=true

# --- Context & Vision ---
BROWSER_USE_MAX_CONTEXT_TOKENS=120000
BROWSER_USE_VISION_QUALITY=medium

# --- Semantic Memory & Embeddings ---
# Note: Enabling semantic memory may require additional setup.
BROWSER_USE_SEMANTIC_MEMORY=false
EMBEDDING_PROVIDER=huggingface
EMBEDDING_MODEL=all-MiniLM-L6-v2
EMBEDDING_DIMS=384

# --- Specific Model Selection ---
GEMINI_MAIN_MODEL=gemini-2.5-flash
GEMINI_PLANNER_MODEL=gemini-2.5-flash
GEMINI_EMBEDDING_MODEL=models/text-embedding-004

# --- Logfire (Remote Logging) ---
# Set to true and provide a token to enable.
BROWSER_USE_LOGFIRE=false
#LOGFIRE_TOKEN=

# --- Smoke Test Conversation Saving ---
SAVE_SMOKE_TEST_CONVERSATIONS=true
SMOKE_TEST_CONVERSATIONS_PATH="./conversations/smoke_tests"

# --- Storage Backend (for multi-agent persistence) ---
#STORAGE_BACKEND=upstash
#UPSTASH_REDIS_REST_URL=
#UPSTASH_REDIS_REST_TOKEN=

# =====================================
# CONFIGURACIÓN BÁSICA REQUERIDA
# =====================================

# =====================================
# CONFIGURACIÓN DEL SERVIDOR
# =====================================

# Host y puerto del backend API
API_HOST=0.0.0.0
API_PORT=8000

# URL base para el frontend (usado por Next.js)
NEXT_PUBLIC_API_BASE_URL=http://localhost/api

# =====================================
# CONFIGURACIÓN DE IA Y MODELOS
# =====================================

# =====================================
# INTEGRACIÓN CON GITHUB (OPCIONAL)
# =====================================

# =====================================
# CONFIGURACIÓN DE MEMORIA Y ALMACENAMIENTO
# =====================================

# Redis URL (opcional, para caché distribuido)
# REDIS_URL=redis://localhost:6379

# Configuración de ChromaDB (memoria semántica)
# CHROMA_HOST=localhost
# CHROMA_PORT=8001

# =====================================
# CONFIGURACIÓN DE BROWSER AUTOMATION
# =====================================

# Configuración de Playwright
PLAYWRIGHT_BROWSER_PATH=/root/.cache/ms-playwright

# Configuración de depuración del navegador
# BROWSER_DEBUG=false
# BROWSER_HEADLESS=true

# =====================================
# CONFIGURACIÓN DE LOGGING Y MONITOREO
# =====================================

# Nivel de logging (DEBUG, INFO, WARNING, ERROR)
LOG_LEVEL=INFO

# Habilitar telemetría (opcional)
# ENABLE_TELEMETRY=false

# Configuración de Logfire (opcional)
# LOGFIRE_TOKEN=your_logfire_token_here

# =====================================
# CONFIGURACIÓN DE DESARROLLO
# =====================================

# Modo de desarrollo (true/false)
# DEVELOPMENT_MODE=false

# Habilitar recarga automática
# AUTO_RELOAD=false

# =====================================
# CONFIGURACIÓN DE SEGURIDAD
# =====================================

# Secreto para JWT (opcional, se genera automáticamente si no se especifica)
# JWT_SECRET=your_jwt_secret_here

# Configuración CORS (opcional)
# CORS_ORIGINS=*

# =====================================
# NOTAS DE CONFIGURACIÓN
# =====================================

# 1. La única variable REQUERIDA es GOOGLE_API_KEY
# 2. Las demás variables tienen valores por defecto
# 3. Para desarrollo local, puedes usar este archivo renombrado a .env
# 4. En producción, configura las variables según tu entorno
# 5. Las variables con prefijo NEXT_PUBLIC_ son visibles en el frontend

MONGODB_URI="mongodb+srv://<db_username>:<db_password>@aeryqak.d8bfir8.mongodb.net/?retryWrites=true&w=majority&appName=AeryQak"
MONGODB_ENVIRONMENT=production 