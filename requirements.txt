# Web framework and API dependencies
fastapi>=0.109.0
uvicorn
websockets<12.0.0
python-multipart
# pydantic is now a dependency of fastapi and beanie, managed automatically
requests>=2.31.0

# Environment and configuration
python-dotenv
logfire[fastapi]>=0.7.0

# Development and testing tools
rich>=13.0.0


# Dependencias de browser-use local
aiofiles>=24.1.0
aiohttp>=3.9.0
anyio>=4.9.0
bubus>=1.1.2
httpx>=0.28.1
markdownify
mem0ai>=0.1.106
patchright>=1.52.5
playwright>=1.52.0
psutil>=7.0.0
pyperclip>=1.9.0
screeninfo>=0.8.1
typing-extensions>=4.12.2
uuid7>=0.1.0
authlib>=1.6.0
opencv-python>=*********

# Dependencias memory
chromadb
sentence-transformers>=4.0.2

# Dependencias CLI
click>=8.1.8
textual>=3.2.0

# AI and language model dependencies
langchain
langchain-google-genai
langchain-openai
langchain-anthropic
langchain-groq
langchain-deepseek
agno


# Data processing and utilities
numpy<2.0
pandas
tabulate
protobuf>=4.20.3
google-api-python-client

# Storage backends (optional)
redis>=5.0.0

# PyTorch - CPU only version for lightweight installation
# Note: Install separately with: pip install torch --index-url https://download.pytorch.org/whl/cpu
torch>=2.3.0

# QAK MongoDB Persistence Dependencies
motor>=3.3.0               # Async MongoDB driver
beanie>=1.24.0             # Async ODM for MongoDB with Pydantic
pydantic-settings>=2.1.0   # Settings management for Pydantic

aioboto3