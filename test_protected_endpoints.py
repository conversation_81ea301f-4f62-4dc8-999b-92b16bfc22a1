#!/usr/bin/env python3
"""
Test script to verify that protected endpoints are working correctly with authentication.
"""

import requests
import json
import sys

BASE_URL = "http://localhost:8000"

def test_unauthenticated_access():
    """Test that unauthenticated requests are properly rejected."""
    print("🔒 Testing unauthenticated access...")
    
    endpoints = [
        ("GET", "/api/projects/"),
        ("POST", "/api/v2/execute"),
        ("GET", "/api/codegen/sessions"),
    ]
    
    for method, endpoint in endpoints:
        try:
            if method == "GET":
                response = requests.get(f"{BASE_URL}{endpoint}")
            elif method == "POST":
                response = requests.post(f"{BASE_URL}{endpoint}", json={"type": "smoke"})
            
            if response.status_code == 401:
                print(f"✅ {method} {endpoint} - Correctly rejected (401)")
            else:
                print(f"❌ {method} {endpoint} - Expected 401, got {response.status_code}")
                
        except Exception as e:
            print(f"❌ {method} {endpoint} - Error: {e}")

def test_authenticated_access():
    """Test that authenticated requests work correctly."""
    print("\n🔑 Testing authenticated access...")
    
    # First, login to get a token
    login_data = {
        "email": "<EMAIL>",
        "password": "SecureAdmin123!"
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        
        if login_response.status_code != 200:
            print(f"❌ Login failed: {login_response.status_code} - {login_response.text}")
            return
        
        login_result = login_response.json()
        access_token = login_result.get("access_token")
        
        if not access_token:
            print("❌ No access token in login response")
            return
        
        print(f"✅ Login successful, token obtained")
        
        # Test authenticated requests
        headers = {"Authorization": f"Bearer {access_token}"}
        
        # Test project routes
        response = requests.get(f"{BASE_URL}/api/projects/", headers=headers)
        if response.status_code == 200:
            print("✅ GET /api/projects/ - Authenticated access works")
        else:
            print(f"❌ GET /api/projects/ - Expected 200, got {response.status_code}")
        
        # Test codegen routes
        response = requests.get(f"{BASE_URL}/api/codegen/sessions", headers=headers)
        if response.status_code == 200:
            print("✅ GET /api/codegen/sessions - Authenticated access works")
        else:
            print(f"❌ GET /api/codegen/sessions - Expected 200, got {response.status_code}")
            
    except Exception as e:
        print(f"❌ Authentication test error: {e}")

def test_user_profile():
    """Test that user profile endpoint works."""
    print("\n👤 Testing user profile access...")
    
    # Login first
    login_data = {
        "email": "<EMAIL>",
        "password": "SecureAdmin123!"
    }
    
    try:
        login_response = requests.post(f"{BASE_URL}/api/auth/login", json=login_data)
        
        if login_response.status_code == 200:
            login_result = login_response.json()
            access_token = login_result.get("access_token")
            headers = {"Authorization": f"Bearer {access_token}"}
            
            # Test profile endpoint
            profile_response = requests.get(f"{BASE_URL}/api/auth/me", headers=headers)
            
            if profile_response.status_code == 200:
                profile_data = profile_response.json()
                print(f"✅ Profile access works - User: {profile_data.get('user', {}).get('email', 'Unknown')}")
            else:
                print(f"❌ Profile access failed: {profile_response.status_code}")
        
    except Exception as e:
        print(f"❌ Profile test error: {e}")

def main():
    """Run all tests."""
    print("🧪 Testing QAK Protected Endpoints")
    print("=" * 50)
    
    # Check if server is running
    try:
        health_response = requests.get(f"{BASE_URL}/api/health")
        if health_response.status_code != 200:
            print("❌ Server is not running or health check failed")
            sys.exit(1)
        print("✅ Server is running")
    except Exception as e:
        print(f"❌ Cannot connect to server: {e}")
        sys.exit(1)
    
    # Run tests
    test_unauthenticated_access()
    test_authenticated_access()
    test_user_profile()
    
    print("\n🎉 Authentication testing completed!")

if __name__ == "__main__":
    main()
